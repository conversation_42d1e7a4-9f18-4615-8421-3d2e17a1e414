services:
    _defaults:
        autowire: true
        autoconfigure: true
        public: false
        bind:
            $projectDirectory: '%kernel.project_dir%'
            $buildDirectory: '%kernel.project_dir%/public/build'
            $directCommandBus: '@Visymo\CommandBus\Infrastructure\CommandBus\Direct\DirectCommandBus'

    _instanceof:
        Infrastructure\UserInterface\Web\Component\ComponentInterface:
            tags: [ 'application.component' ]
        Infrastructure\UserInterface\Web\Form\Brand\Validator\BrandFormValidatorInterface:
            tags: [ 'infrastructure.form.brand_validator' ]
        Infrastructure\UserInterface\Web\Form\Brand\AbstractBrandModuleType:
            tags: [ 'infrastructure.form.brand_module_type' ]
        Infrastructure\UserInterface\Web\Url\Persistent\PersistentUrlParametersProviderInterface:
            tags: [ 'infrastructure.persistent_url_parameters_provider' ]

    # Console
    Infrastructure\UserInterface\Console\:
        resource: '%kernel.project_dir%/src/Infrastructure/UserInterface/Console/**/*'

    # Develop host helper
    Infrastructure\UserInterface\Web\Helper\DevelopHostHelper:
        arguments:
            $devVmName: ~

    # Web
    Infrastructure\UserInterface\Web\:
        resource: '%kernel.project_dir%/src/Infrastructure/UserInterface/Web/**/*'

    # Config API
    Infrastructure\UserInterface\Web\Controller\ConfigApi\ConfigApiFileTreeController:
        arguments:
            $configApiClient: '@infrastructure.service.config_api.client'

    Visymo\Filesystem\File\FileFactory: ~

    infrastructure.json_template.example_file:
        class: Visymo\Filesystem\File\FileInterface
        factory: [ '@Visymo\Filesystem\File\FileFactory', 'create' ]
        arguments:
            $filePath: '%json_template_example_file_path%'

    Infrastructure\UserInterface\Web\Controller\JsonTemplate\JsonTemplateController:
        arguments:
            $jsonTemplateExampleFile: '@infrastructure.json_template.example_file'

    # Menu
    Infrastructure\UserInterface\Web\Menu\Helper\MenuHelper:
        arguments:
            $menuItemBuilders: !tagged_iterator app.menu_item_builder

    # User
    Domain\User\User:
        factory: [ '@Infrastructure\UserInterface\Web\Security\Helper\UserHelper', 'getUser' ]

    # VBB Style ID
    infrastructure.vbb_style_id.vbb_style_id_schema_validator:
        class: Visymo\Shared\Domain\Validator\JsonSchemaValidator
        factory: [ '@Visymo\Shared\Infrastructure\Bridge\Opis\OpisJsonSchemaValidatorFactory', 'create' ]
        arguments:
            - '%vbb_style_id_json_schema_file_path%'

    Infrastructure\Service\VbbStyleId\VbbStyleIdValidator:
        arguments:
            $vbbStyleIdJsonSchemaValidator: '@infrastructure.vbb_style_id.vbb_style_id_schema_validator'

    Infrastructure\UserInterface\Web\Form\Brand\BrandType:
        arguments:
            $validators: !tagged_iterator infrastructure.form.brand_validator
            $brandModuleForms: !tagged_iterator infrastructure.form.brand_module_type

    Infrastructure\UserInterface\Web\Url\Persistent\PersistentUrlParametersHelper:
        arguments:
            $parameterProviders: !tagged_iterator infrastructure.persistent_url_parameters_provider

when@dev: &dev
    services:
        Infrastructure\UserInterface\Web\Helper\DevelopHostHelper:
            arguments:
                $devVmName: '%env(DEV_VM_NAME)%'

when@dev_direct: *dev

when@test:
    services:
        Tests\Integration\Infrastructure\UserInterface\Web\Component\ComponentDataProvider:
            public: true
            arguments:
                $twigComponentsYamlFile: '%kernel.project_dir%/config/packages/twig_component.yaml'
                $components: !tagged_iterator application.component
