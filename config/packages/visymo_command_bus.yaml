visymo_command_bus:
    retryable_commands:
        defaults:
            retry_on_exceptions:
                - Domain\UrlScan\Exception\UrlContentScanFailedException
    routing_config:
        default: command
        Application\Command\Brand\PushBrandAssets\PushBrandAssetsCommand: '%queue_serp_config_push%'
        Application\Command\Brand\PushBrandConfig\PushBrandConfigCommand: '%queue_serp_config_push%'
        Application\Command\Brand\PushBrandWebsitesProjectConfig\PushBrandWebsitesProjectConfigCommand: '%queue_serp_config_push%'
        Application\Command\Brand\PushBrands\PushBrandsCommand: '%queue_serp_config_push%'
        Application\Command\StyleId\PushBrandStyleIds\PushBrandStyleIdsCommand: '%queue_serp_config_push%'
        Application\Command\Generic\Wait\WaitCommand: '%queue_wait%'
        Application\Command\Scraper\ScheduleScraperBatch\ScheduleScraperBatchCommand: '%queue_scraper%'
        Application\Command\Scraper\ScheduleScraperRun\ScheduleScraperRunCommand: '%queue_scraper%'
        Application\Command\Scraper\UpdateScraperBatchUrl\UpdateScraperBatchUrlCommand: '%queue_scraper%'
        Application\Command\Scraper\UpdateScraperBatchStatus\UpdateScraperBatchStatusCommand: '%queue_scraper%'
    rabbit_mq:
        enabled: true
        host: '%env(MQ_HOST)%'
        port: '%env(MQ_PORT)%'
        api_port: '%env(MQ_API_PORT)%'
        user: '%env(MQ_USER)%'
        password: '%env(MQ_PASSWORD)%'
        vhost: '%env(MQ_VHOST)%'

when@dev_direct:
    services:
        Visymo\CommandBus\Domain\CommandBus\CommandBusInterface: '@Visymo\CommandBus\Infrastructure\CommandBus\Direct\DirectCommandBus'
