<?php

declare(strict_types=1);

namespace Tests\Integration\PageHeadTags\EventSubscriber;

use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\PageHeadTags\EventSubscriber\PageHeadTagsEventSubscriber;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class PageHeadTagsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            PageHeadTagsEventSubscriber::class,
            'onJsonTemplateHandled',
        );

        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            PageHeadTagsEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }
}
