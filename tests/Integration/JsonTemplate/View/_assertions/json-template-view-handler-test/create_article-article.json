{"composite_search_api_requests": [{"content_page": {"enabled": true, "is_homepage": null, "public_id": null, "paragraph_amount": null, "is_adult": null}, "content_pages": {"enabled": true, "is_homepage": null, "public_ids": null, "excluded_public_ids": null, "relevant_for_public_id": null, "category_public_id": null, "has_image": true, "sort": "date_created_desc", "page_size": 11, "page": 1, "paragraph_amount": 0, "is_adult": null}, "related_terms": {"enabled": true, "amount": 6}}], "container": {"id": "container-0", "type": "container", "layout": "article", "mode": null, "font": "Inter", "components": [{"id": "columns_range-16", "type": "columns_range", "layout": "default", "start": 1, "end": 3, "components": [{"id": "search_header-17", "type": "search_header", "componentSpaceModifiers": [], "layout": "content-1", "autofocus": false, "logoDarkMode": false, "logoStyleFilter": null, "showBackgroundOnDesktop": false, "showBackgroundOnMobile": false, "showBackgroundOnTablet": false, "showHamburgerMenuOnDesktop": false, "showHamburgerMenuOnMobile": false, "showHamburgerMenuOnTablet": false, "showMainMenuMoreOnDesktop": false, "showMainMenuMoreOnMobile": false, "showMainMenuMoreOnTablet": false, "showMainMenuOnDesktop": false, "showMainMenuOnMobile": false, "showMainMenuOnTablet": false, "showSubMenuOnDesktop": false, "showSubMenuOnMobile": false, "showSubMenuOnTablet": false, "showSearchQuery": false}], "section": "search", "sectionVisible": true, "sectionCssProperties": ["background", "box-shadow"]}, {"id": "columns_range-18", "type": "columns_range", "layout": "default", "start": 2, "end": 2, "components": [{"id": "content_page_header-7", "type": "content_page_header", "componentSpaceModifiers": ["top-l"], "layout": "default", "showReadTime": true, "showPageNumber": true}, {"id": "content_page_title-8", "type": "content_page_title", "componentSpaceModifiers": [], "layout": "default"}], "section": null, "sectionVisible": true, "sectionCssProperties": []}, {"id": "columns-19", "type": "columns", "layout": "default", "one": [{"id": "has_content_page-20", "type": "has_content_page", "matchingSegment": [{"id": "table_of_contents-9", "type": "table_of_contents", "componentSpaceModifiers": ["top-l", "bottom-l"], "layout": "default"}], "nonMatchingSegment": []}], "two": [{"id": "has_content_page-21", "type": "has_content_page", "matchingSegment": [{"id": "content_page_excerpt-10", "type": "content_page_excerpt", "componentSpaceModifiers": ["bottom-l"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}, {"id": "content_page_image-11", "type": "content_page_image", "componentSpaceModifiers": ["top-l", "bottom-l"], "layout": "large", "align": "center", "showCaption": true, "fallbackImage": null}], "nonMatchingSegment": [{"id": "title-22", "type": "title", "componentSpaceModifiers": ["top-l"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.title", "titleHighlightTranslationId": null, "subtitleTranslationId": "content_page.subtitle", "layout": "dsr"}, {"id": "organic_results_with_fallback-23", "type": "organic_results_with_fallback", "resultsComponent": {"id": "organic_content_page_results-1", "type": "organic_content_page_results", "componentSpaceModifiers": ["bottom", "top"], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "resultImageLink": true, "showResultDisplayUrl": true, "linkToActiveBrand": true, "maxDescriptionLength": 130, "layout": "dsr"}, "fallbackComponent": {"id": "organic_results-2", "type": "organic_results", "componentSpaceModifiers": ["bottom", "top"], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": 130, "layout": "dsr"}}]}, {"id": "google_related_terms-3", "type": "google_related_terms", "amount": 6, "route": "route_display_search_related_web", "target": "content", "termsUrlParameterEnabled": true, "container": "csa-related-1", "fallbackRelatedTerms": {"componentSpaceModifiers": [], "layout": "fallback", "amount": 6, "zone": "i", "route": "route_display_search_related_web", "columns": 1, "showTitle": true, "keywordHighlight": null, "repeatTerms": true}}, {"id": "has_content_page-24", "type": "has_content_page", "matchingSegment": [{"id": "content_page_paragraphs-4", "type": "content_page_paragraphs", "componentSpaceModifiers": [], "layout": "container", "amount": null, "paragraphComponent": {"componentSpaceModifiers": ["bottom-l"], "maxLength": null, "startAfterLength": null, "splitOnLineEnd": false, "layout": "default"}}, {"id": "content_page_footer-12", "type": "content_page_footer", "componentSpaceModifiers": [], "layout": "default"}, {"id": "share_page-13", "type": "share_page", "componentSpaceModifiers": [], "layout": "default", "share": "content_page"}, {"id": "content_page_results-15", "type": "content_page_results", "componentSpaceModifiers": ["top-xxl", "bottom-xxl"], "amountInRow": 3, "ignoreQuery": false, "resetCounter": true, "layout": "card-4", "titleComponent": {"componentSpaceModifiers": ["top-xl"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.top_stories.title", "titleHighlightTranslationId": "content_page.top_stories.title_highlight", "subtitleTranslationId": null, "layout": "content-3"}}], "nonMatchingSegment": [{"id": "organic_results_with_fallback-25", "type": "organic_results_with_fallback", "resultsComponent": {"id": "organic_content_page_results-5", "type": "organic_content_page_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "resultImageLink": true, "showResultDisplayUrl": true, "linkToActiveBrand": true, "maxDescriptionLength": null, "layout": "dsr"}, "fallbackComponent": {"id": "organic_results-6", "type": "organic_results", "componentSpaceModifiers": [], "resultDescriptionMoreLink": false, "resultDisplayUrlLink": false, "resultTitleLink": true, "showResultDisplayUrl": true, "maxDescriptionLength": null, "layout": "dsr"}}, {"id": "organic_error_message-26", "type": "organic_error_message"}]}], "three": [{"id": "content_page_results-14", "type": "content_page_results", "componentSpaceModifiers": ["bottom-l"], "amountInRow": 1, "ignoreQuery": false, "resetCounter": true, "layout": "rank-list-1", "titleComponent": {"componentSpaceModifiers": ["top-l", "bottom"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.popular_articles", "titleHighlightTranslationId": null, "subtitleTranslationId": null, "layout": "content-4"}}], "mainColumn": null, "section": null, "sectionVisible": true, "sectionCssProperties": []}, {"id": "footer-27", "type": "footer", "layout": "default", "components": [{"id": "columns_range-28", "type": "columns_range", "layout": "default", "start": 1, "end": 3, "components": [{"id": "footer_logo-29", "type": "footer_logo", "layout": "default", "logoDarkMode": false, "logoStyleFilter": null, "hideOnDesktop": true}, {"id": "disclaimer-30", "type": "disclaimer", "componentSpaceModifiers": [], "layout": "default"}], "section": "footer", "sectionVisible": true, "sectionCssProperties": ["background"]}, {"id": "footer_navigation-31", "type": "footer_navigation", "componentSpaceModifiers": [], "showAbout": true, "showContact": true, "showCopyright": true, "showDisclaimer": true, "showPrivacy": true, "layout": "dsr", "logoDarkMode": false}]}, {"id": "scroll_to_top-32", "type": "scroll_to_top", "layout": "default"}]}}