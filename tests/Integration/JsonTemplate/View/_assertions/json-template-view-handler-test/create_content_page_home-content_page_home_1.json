{"composite_search_api_requests": [{"content_page_categories": {"enabled": true, "collection_slug": "collection", "max_level": 0, "public_ids": [], "excluded_public_ids": [], "has_image": true, "is_adult": false}, "content_pages": {"enabled": true, "is_homepage": null, "public_ids": null, "excluded_public_ids": null, "relevant_for_public_id": null, "category_public_id": null, "has_image": true, "sort": "date_created_desc", "page_size": 28, "page": 1, "paragraph_amount": 0, "is_adult": null}}], "container": {"id": "container-0", "type": "container", "layout": "content-home-1", "mode": null, "font": "Inter", "components": [{"id": "columns-6", "type": "columns", "layout": "default", "one": [{"id": "search_header-1", "type": "search_header", "componentSpaceModifiers": [], "layout": "content-category-1", "autofocus": false, "logoDarkMode": false, "logoStyleFilter": null, "showBackgroundOnDesktop": false, "showBackgroundOnMobile": false, "showBackgroundOnTablet": false, "showHamburgerMenuOnDesktop": false, "showHamburgerMenuOnMobile": false, "showHamburgerMenuOnTablet": false, "showMainMenuMoreOnDesktop": false, "showMainMenuMoreOnMobile": false, "showMainMenuMoreOnTablet": false, "showMainMenuOnDesktop": false, "showMainMenuOnMobile": false, "showMainMenuOnTablet": false, "showSubMenuOnDesktop": false, "showSubMenuOnMobile": false, "showSubMenuOnTablet": false, "showSearchQuery": true}], "two": [], "three": [], "mainColumn": null, "section": "search", "sectionVisible": true, "sectionCssProperties": ["box-shadow"]}, {"id": "columns-7", "type": "columns", "layout": "default", "one": [{"id": "content_page_category_results-2", "type": "content_page_category_results", "componentSpaceModifiers": ["top-l", "bottom-xl"], "title": null, "maxLevel": 0, "hasContentPagesWithImage": true, "layout": "default"}], "two": [], "three": [], "mainColumn": null, "section": "search-category", "sectionVisible": false, "sectionCssProperties": ["background", "box-shadow"]}, {"id": "columns-8", "type": "columns", "layout": "default", "one": [{"id": "content_page_results-3", "type": "content_page_results", "componentSpaceModifiers": ["bottom-xxl"], "amountInRow": 2, "ignoreQuery": false, "resetCounter": true, "layout": "card-3", "titleComponent": {"componentSpaceModifiers": ["top-l", "bottom-xl"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.from_the_editors.title", "titleHighlightTranslationId": null, "subtitleTranslationId": "content_page.from_the_editors.title_subtitle", "layout": "content-1"}}], "two": [], "three": [], "mainColumn": null, "section": null, "sectionVisible": true, "sectionCssProperties": []}, {"id": "columns-9", "type": "columns", "layout": "default", "one": [{"id": "content_page_results-4", "type": "content_page_results", "componentSpaceModifiers": ["bottom-xxl"], "amountInRow": 4, "ignoreQuery": false, "resetCounter": true, "layout": "card-3", "titleComponent": {"componentSpaceModifiers": ["top-xl", "bottom-xl"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.top_stories.title", "titleHighlightTranslationId": "content_page.top_stories.title_highlight", "subtitleTranslationId": null, "layout": "content-2"}}], "two": [], "three": [], "mainColumn": null, "section": "content", "sectionVisible": true, "sectionCssProperties": ["border-top"]}, {"id": "columns-10", "type": "columns", "layout": "default", "one": [{"id": "content_page_results-5", "type": "content_page_results", "componentSpaceModifiers": ["bottom-xxl"], "amountInRow": 4, "ignoreQuery": false, "resetCounter": true, "layout": "card-3", "titleComponent": {"componentSpaceModifiers": ["top-xl", "bottom-xl"], "title": null, "titleHighlight": null, "titleTranslationId": "content_page.more_top_stories.title", "titleHighlightTranslationId": "content_page.more_top_stories.title_highlight", "subtitleTranslationId": null, "layout": "content-2"}}], "two": [], "three": [], "mainColumn": null, "section": "content", "sectionVisible": true, "sectionCssProperties": ["border-top"]}, {"id": "footer-11", "type": "footer", "layout": "default", "components": [{"id": "columns-12", "type": "columns", "layout": "default", "one": [{"id": "footer_logo-13", "type": "footer_logo", "layout": "default", "logoDarkMode": false, "logoStyleFilter": null, "hideOnDesktop": false}, {"id": "disclaimer-14", "type": "disclaimer", "componentSpaceModifiers": [], "layout": "default"}], "two": [], "three": [], "mainColumn": null, "section": "footer", "sectionVisible": true, "sectionCssProperties": ["background"]}, {"id": "footer_navigation-15", "type": "footer_navigation", "componentSpaceModifiers": [], "showAbout": true, "showContact": true, "showCopyright": true, "showDisclaimer": true, "showPrivacy": true, "layout": "dsr", "logoDarkMode": false}]}]}}