<?php

declare(strict_types=1);

namespace Tests\Integration\JsonTemplate\EventSubscriber;

use App\JsonTemplate\EventSubscriber\JsonTemplateEventSubscriber;
use App\JsonTemplate\Template\JsonTemplate;
use App\JsonTemplate\Template\Options\JsonTemplateOptions;
use App\JsonTemplate\View\Event\JsonTemplateViewCreatedEvent;
use App\JsonTemplate\View\JsonTemplateViewFactory;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class JsonTemplateEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function setUp(): void
    {
        self::ensureKernelShutdown();

        parent::setUp();

        // Set up a request stub FIRST before any container access
        $request = Request::create('https://id.brand.com/test');
        $this->stubs()->request()->getMainRequest()->setRequest($request);

        // Use the food brand configuration which has proper domain mappings
        self::websiteSettingsTestHelper()->injectFoodBrandWebsiteConfiguration();

        $brandSettingsStub = $this->stubs()->brandSettings();
        $brandSettingsStub->setSlug('food');
        $brandSettingsStub->setPartnerSlug('partner');

        $websiteSettingsStub = $this->stubs()->websiteSettings();
        self::websiteSettingsTestHelper()->injectWebsiteSettings($websiteSettingsStub);
        self::brandSettingsTestHelper()->injectBrandSettings($brandSettingsStub);
    }

    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateViewCreatedEvent::NAME,
            JsonTemplateEventSubscriber::class,
            'onJsonTemplateViewCreated',
        );
    }

    public function testOnJsonTemplateViewCreated(): void
    {
        /** @var JsonTemplateViewFactory $jsonTemplateViewFactory */
        $jsonTemplateViewFactory = self::getContainer()->get(JsonTemplateViewFactory::class);
        /** @var string $projectDir */
        $projectDir = self::getContainer()->getParameter('kernel.project_dir');

        // Create a view using the factory - this will naturally create the JsonTemplate with the correct file path
        $view = $jsonTemplateViewFactory->create(
            jsonTemplateFile: '@shared/templates_json/article/article.json',
            response        : new Response(),
        );

        // The JsonTemplateEventSubscriber should have already been called during view creation
        // due to the event dispatcher in JsonTemplateViewFactory, but let's call it explicitly to test
        $jsonTemplateEventSubscriber = new JsonTemplateEventSubscriber(
            projectDir: $projectDir,
        );
        $jsonTemplateEventSubscriber->onJsonTemplateViewCreated(
            new JsonTemplateViewCreatedEvent($view),
        );

        // The header should contain the relative path from project dir
        $actualHeader = $view->getResponse()->headers->get('X-Log-Json_Template_File');
        self::assertStringContainsString('/templates_json/article/article.json', $actualHeader);
        self::assertStringStartsWith('/', $actualHeader);
    }
}
