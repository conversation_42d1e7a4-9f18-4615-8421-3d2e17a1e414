<?php

declare(strict_types=1);

namespace Tests\Integration\Cheq;

use App\Cheq\EventSubscriber\CheqEventSubscriber;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class CheqEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            CheqEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }
}
