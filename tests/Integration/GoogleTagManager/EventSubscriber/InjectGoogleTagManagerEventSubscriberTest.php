<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleTagManager\EventSubscriber;

use App\CookieConsent\Helper\CookieConsentHelper;
use App\GeoIp\GeoIp2CountryHelper;
use App\GoogleTagManager\EventSubscriber\InjectGoogleTagManagerEventSubscriber;
use App\GoogleTagManager\Settings\GoogleTagManagerSettings;
use App\Template\Event\RenderTemplateFootersEvent;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Twig\Environment;

class InjectGoogleTagManagerEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            InjectGoogleTagManagerEventSubscriber::class,
            'renderTemplateHeaders',
        );
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateFootersEvent::NAME,
            InjectGoogleTagManagerEventSubscriber::class,
            'renderTemplateFooters',
        );
    }

    public function testRenderTemplateHeadersAddsItemWhenEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $cookieConsentHelper = $this->createMock(CookieConsentHelper::class);
        $geoIpCountryHelper = $this->createMock(GeoIp2CountryHelper::class);
        $settings = $this->stubs()->moduleSettings()->getGoogleTagManager()
            ->setEnabled(true)
            ->setEnabledForRequest(true)
            ->setGoogleTagManagerId('GTM-TEST123')
            ->create();

        $cookieConsentHelper->method('consent')->willReturn(true);
        $geoIpCountryHelper->method('isEuVisitor')->willReturn(false);
        $twig->method('render')
            ->with(
                '@theme/google_tag_manager/google_tag_manager_header.html.twig',
                [
                    'google_tag_manager_id'    => 'GTM-TEST123',
                    'apply_gtm_cookie_consent' => true,
                ],
            )
            ->willReturn('header-content');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleTagManagerEventSubscriber(
            $twig,
            $cookieConsentHelper,
            $geoIpCountryHelper,
            $settings,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('header-content', $event->getItems()[0]);
    }

    public function testRenderTemplateHeadersDoesNothingWhenDisabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $cookieConsentHelper = $this->createMock(CookieConsentHelper::class);
        $geoIpCountryHelper = $this->createMock(GeoIp2CountryHelper::class);
        $settings = $this->stubs()->moduleSettings()->getGoogleTagManager()
            ->setEnabled(false)
            ->setEnabledForRequest(true)
            ->setGoogleTagManagerId('GTM-TEST123')
            ->create();

        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleTagManagerEventSubscriber(
            $twig,
            $cookieConsentHelper,
            $geoIpCountryHelper,
            $settings,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }

    public function testRenderTemplateHeadersDoesNothingWhenNotEnabledForRequest(): void
    {
        $twig = $this->createMock(Environment::class);
        $cookieConsentHelper = $this->createMock(CookieConsentHelper::class);
        $geoIpCountryHelper = $this->createMock(GeoIp2CountryHelper::class);
        $settings = $this->stubs()->moduleSettings()->getGoogleTagManager()
            ->setEnabled(true)
            ->setEnabledForRequest(false)
            ->setGoogleTagManagerId('GTM-TEST123')
            ->create();

        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleTagManagerEventSubscriber(
            $twig,
            $cookieConsentHelper,
            $geoIpCountryHelper,
            $settings,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertEmpty($event->getItems());
    }

    public function testRenderTemplateHeadersWithEuVisitorAndNoConsent(): void
    {
        $twig = $this->createMock(Environment::class);
        $cookieConsentHelper = $this->createMock(CookieConsentHelper::class);
        $geoIpCountryHelper = $this->createMock(GeoIp2CountryHelper::class);
        $settings = $this->stubs()->moduleSettings()->getGoogleTagManager()
            ->setEnabled(true)
            ->setEnabledForRequest(true)
            ->setGoogleTagManagerId('GTM-TEST123')
            ->create();

        $cookieConsentHelper->method('consent')->willReturn(false);
        $geoIpCountryHelper->method('isEuVisitor')->willReturn(true);
        $twig->method('render')
            ->with(
                '@theme/google_tag_manager/google_tag_manager_header.html.twig',
                [
                    'google_tag_manager_id'    => 'GTM-TEST123',
                    'apply_gtm_cookie_consent' => false,
                ],
            )
            ->willReturn('header-content');

        $event = new RenderTemplateHeadersEvent();
        $subscriber = new InjectGoogleTagManagerEventSubscriber(
            $twig,
            $cookieConsentHelper,
            $geoIpCountryHelper,
            $settings,
        );
        $subscriber->renderTemplateHeaders($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('header-content', $event->getItems()[0]);
    }

    public function testRenderTemplateFootersAddsItemWhenEnabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $cookieConsentHelper = $this->createMock(CookieConsentHelper::class);
        $geoIpCountryHelper = $this->createMock(GeoIp2CountryHelper::class);
        $settings = new GoogleTagManagerSettings(
            enabled           : true,
            enabledForRequest : true,
            googleTagManagerId: 'GTM-TEST123',
        );

        $twig->method('render')
            ->with(
                '@theme/google_tag_manager/google_tag_manager_footer.html.twig',
                ['google_tag_manager_id' => 'GTM-TEST123'],
            )
            ->willReturn('footer-content');

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectGoogleTagManagerEventSubscriber(
            $twig,
            $cookieConsentHelper,
            $geoIpCountryHelper,
            $settings,
        );
        $subscriber->renderTemplateFooters($event);

        self::assertCount(1, $event->getItems());
        self::assertSame('footer-content', $event->getItems()[0]);
    }

    public function testRenderTemplateFootersDoesNothingWhenDisabled(): void
    {
        $twig = $this->createMock(Environment::class);
        $cookieConsentHelper = $this->createMock(CookieConsentHelper::class);
        $geoIpCountryHelper = $this->createMock(GeoIp2CountryHelper::class);
        $settings = new GoogleTagManagerSettings(
            enabled           : false,
            enabledForRequest : true,
            googleTagManagerId: 'GTM-TEST123',
        );

        $twig->expects($this->never())->method('render');

        $event = new RenderTemplateFootersEvent();
        $subscriber = new InjectGoogleTagManagerEventSubscriber(
            $twig,
            $cookieConsentHelper,
            $geoIpCountryHelper,
            $settings,
        );
        $subscriber->renderTemplateFooters($event);

        self::assertEmpty($event->getItems());
    }
}
