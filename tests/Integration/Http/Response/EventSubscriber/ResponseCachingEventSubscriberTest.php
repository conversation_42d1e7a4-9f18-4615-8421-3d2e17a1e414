<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Response\EventSubscriber;

use App\Http\Response\EventSubscriber\ResponseCachingEventSubscriber;
use App\Kernel\KernelResponseEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class ResponseCachingEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::NO_REDIRECT->value,
            ResponseCachingEventSubscriber::class,
            'enableResponseCaching',
        );
    }
}
