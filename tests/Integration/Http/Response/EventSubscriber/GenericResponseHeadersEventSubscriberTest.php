<?php

declare(strict_types=1);

namespace Tests\Integration\Http\Response\EventSubscriber;

use App\Http\Request\GenericRequestInterface;
use App\Http\Request\Info\RequestInfoInterface;
use App\Http\Response\EventSubscriber\GenericResponseHeadersEventSubscriber;
use App\Http\Response\HeaderLink\HeaderLink;
use App\Http\Response\HeaderLink\HeaderLinkAs;
use App\Http\Response\HeaderLink\HeaderLinkRegistry;
use App\Http\Response\HeaderLink\HeaderLinkRel;
use App\Kernel\KernelResponseEvent;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class GenericResponseHeadersEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::NO_REDIRECT->value,
            GenericResponseHeadersEventSubscriber::class,
            'onResponse',
        );

        $this->assertEventListenerInstanceIsRegistered(
            KernelResponseEvent::REDIRECT->value,
            GenericResponseHeadersEventSubscriber::class,
            'onResponse',
        );
    }

    public function testOnResponseSetsGenericHeaders(): void
    {
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $genericRequest = $this->createMock(GenericRequestInterface::class);
        $headerLinkRegistry = new HeaderLinkRegistry();

        $requestInfo->method('getLocale')->willReturn('en');
        $genericRequest->method('getPageviewId')->willReturn('pageview-123');
        $genericRequest->method('getVisitId')->willReturn('visit-456');

        $subscriber = new GenericResponseHeadersEventSubscriber(
            $requestInfo,
            $genericRequest,
            $headerLinkRegistry
        );

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->onResponse($responseEvent);

        self::assertSame('en', $response->headers->get('X-Log-Locale'));
        self::assertSame('pageview-123', $response->headers->get('X-Log-Pageview_Id'));
        self::assertSame('visit-456', $response->headers->get('X-Log-Visit_Id'));
    }

    public function testOnResponseWithDifferentLocales(): void
    {
        $testLocales = ['en', 'de', 'fr', 'es', 'it'];

        foreach ($testLocales as $locale) {
            $requestInfo = $this->createMock(RequestInfoInterface::class);
            $genericRequest = $this->createMock(GenericRequestInterface::class);
            $headerLinkRegistry = new HeaderLinkRegistry();

            $requestInfo->method('getLocale')->willReturn($locale);
            $genericRequest->method('getPageviewId')->willReturn('pageview-123');
            $genericRequest->method('getVisitId')->willReturn('visit-456');

            $subscriber = new GenericResponseHeadersEventSubscriber(
                $requestInfo,
                $genericRequest,
                $headerLinkRegistry
            );

            $response = new Response();
            $responseEvent = new ResponseEvent(
                $this->createMock(HttpKernelInterface::class),
                new Request(),
                HttpKernelInterface::MAIN_REQUEST,
                $response
            );

            $subscriber->onResponse($responseEvent);

            self::assertSame($locale, $response->headers->get('X-Log-Locale'));
        }
    }

    public function testOnResponseAddsHeaderLinksToResponse(): void
    {
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $genericRequest = $this->createMock(GenericRequestInterface::class);
        $headerLinkRegistry = new HeaderLinkRegistry();

        $requestInfo->method('getLocale')->willReturn('en');
        $genericRequest->method('getPageviewId')->willReturn('pageview-123');
        $genericRequest->method('getVisitId')->willReturn('visit-456');

        // Add some header links
        $headerLinkRegistry->add(
            new HeaderLink(
                url: 'https://example.com/style.css',
                rel: HeaderLinkRel::PRELOAD
            )
        );
        $headerLinkRegistry->add(
            new HeaderLink(
                url: 'https://example.com/script.js',
                rel: HeaderLinkRel::PRELOAD,
                as: HeaderLinkAs::SCRIPT
            )
        );

        $subscriber = new GenericResponseHeadersEventSubscriber(
            $requestInfo,
            $genericRequest,
            $headerLinkRegistry
        );

        $response = new Response();
        $responseEvent = new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response
        );

        $subscriber->onResponse($responseEvent);

        self::assertTrue($response->headers->has('Link'));
        $linkHeader = $response->headers->get('Link');
        self::assertNotNull($linkHeader);
        self::assertStringContainsString('https://example.com/style.css', $linkHeader);
        self::assertStringContainsString('https://example.com/script.js', $linkHeader);
    }
}
