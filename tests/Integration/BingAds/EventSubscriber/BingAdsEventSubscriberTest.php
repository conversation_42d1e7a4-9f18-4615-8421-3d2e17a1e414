<?php

declare(strict_types=1);

namespace Tests\Integration\BingAds\EventSubscriber;

use App\BingAds\EventSubscriber\BingAdsEventSubscriber;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class BingAdsEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            BingAdsEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }
}
