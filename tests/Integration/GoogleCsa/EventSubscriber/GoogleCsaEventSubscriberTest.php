<?php

declare(strict_types=1);

namespace Tests\Integration\GoogleCsa\EventSubscriber;

use App\Debug\Request\DebugRequestInterface;
use App\GoogleCsa\EventSubscriber\GoogleCsaEventSubscriber;
use App\GoogleCsa\Factory\GoogleCsaFactory;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\GoogleCsa\Request\GoogleCsaRequestFactory;
use App\Http\Request\Info\RequestInfoInterface;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\ViewInterface;
use App\TrademarkInfringement\TrademarkInfringementResultBlocker;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;
use Tests\Stub\GoogleCsa\GoogleCsaStubBuilder;
use Tests\Stub\GoogleCsa\Request\GoogleCsaRequestStub;

class GoogleCsaEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            JsonTemplateHandledEvent::NAME,
            GoogleCsaEventSubscriber::class,
            'onJsonTemplateHandled',
        );
    }

    public function testOnJsonTemplateHandledCreatesAndRegistersGoogleCsa(): void
    {
        $googleCsaRequestFactory = $this->createMock(GoogleCsaRequestFactory::class);
        $googleCsaFactory = $this->createMock(GoogleCsaFactory::class);
        $googleCsaRegistry = new GoogleCsaRegistry();
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);

        $view = $this->createMock(ViewInterface::class);
        $googleCsaRequest = new GoogleCsaRequestStub();
        $googleCsa = (new GoogleCsaStubBuilder())->withPublisherId('test-publisher')->create();

        $debugRequest->method('disableGoogleAds')->willReturn(false);
        $requestInfo->method('getUserAgent')->willReturn('Mozilla/5.0');
        $trademarkInfringementResultBlocker->method('blockResults')->willReturn(false);
        $googleCsaRequestFactory->method('createFromView')->with($view)->willReturn($googleCsaRequest);
        $googleCsaFactory->method('create')->with($googleCsaRequest)->willReturn($googleCsa);

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );
        $subscriber->onJsonTemplateHandled($event);

        self::assertSame($googleCsa, $googleCsaRegistry->getGoogleCsa());
    }

    public function testOnJsonTemplateHandledDoesNothingWhenGoogleAdsDisabled(): void
    {
        $googleCsaRequestFactory = $this->createMock(GoogleCsaRequestFactory::class);
        $googleCsaFactory = $this->createMock(GoogleCsaFactory::class);
        $googleCsaRegistry = new GoogleCsaRegistry();
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);

        $view = $this->createMock(ViewInterface::class);

        $debugRequest->method('disableGoogleAds')->willReturn(true);
        $googleCsaRequestFactory->expects($this->never())->method('createFromView');
        $googleCsaFactory->expects($this->never())->method('create');

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );
        $subscriber->onJsonTemplateHandled($event);

        self::assertNull($googleCsaRegistry->getGoogleCsa());
    }

    public function testOnJsonTemplateHandledDoesNothingWhenPageburstBot(): void
    {
        $googleCsaRequestFactory = $this->createMock(GoogleCsaRequestFactory::class);
        $googleCsaFactory = $this->createMock(GoogleCsaFactory::class);
        $googleCsaRegistry = new GoogleCsaRegistry();
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);

        $view = $this->createMock(ViewInterface::class);

        $debugRequest->method('disableGoogleAds')->willReturn(false);
        $requestInfo->method('getUserAgent')->willReturn('Mozilla/5.0 (compatible; PageBurst Bot)');
        $googleCsaRequestFactory->expects($this->never())->method('createFromView');
        $googleCsaFactory->expects($this->never())->method('create');

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );
        $subscriber->onJsonTemplateHandled($event);

        self::assertNull($googleCsaRegistry->getGoogleCsa());
    }

    public function testOnJsonTemplateHandledDoesNothingWhenTrademarkInfringementBlocked(): void
    {
        $googleCsaRequestFactory = $this->createMock(GoogleCsaRequestFactory::class);
        $googleCsaFactory = $this->createMock(GoogleCsaFactory::class);
        $googleCsaRegistry = new GoogleCsaRegistry();
        $debugRequest = $this->createMock(DebugRequestInterface::class);
        $requestInfo = $this->createMock(RequestInfoInterface::class);
        $trademarkInfringementResultBlocker = $this->createMock(TrademarkInfringementResultBlocker::class);

        $view = $this->createMock(ViewInterface::class);

        $debugRequest->method('disableGoogleAds')->willReturn(false);
        $requestInfo->method('getUserAgent')->willReturn('Mozilla/5.0');
        $trademarkInfringementResultBlocker->method('blockResults')->willReturn(true);
        $googleCsaRequestFactory->expects($this->never())->method('createFromView');
        $googleCsaFactory->expects($this->never())->method('create');

        $event = new JsonTemplateHandledEvent($view);
        $subscriber = new GoogleCsaEventSubscriber(
            $googleCsaRequestFactory,
            $googleCsaFactory,
            $googleCsaRegistry,
            $debugRequest,
            $requestInfo,
            $trademarkInfringementResultBlocker
        );
        $subscriber->onJsonTemplateHandled($event);

        self::assertNull($googleCsaRegistry->getGoogleCsa());
    }
}
