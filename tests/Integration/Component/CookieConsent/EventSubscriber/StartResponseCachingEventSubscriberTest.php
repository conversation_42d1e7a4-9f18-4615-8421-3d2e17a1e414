<?php

declare(strict_types=1);

namespace Tests\Integration\Component\CookieConsent\EventSubscriber;

use App\Component\Generic\CookieConsent\CookieConsentRenderer;
use App\Component\Generic\CookieConsent\EventSubscriber\StartResponseCachingEventSubscriber;
use App\Http\Response\Event\ResponseCachingStartedEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

class StartResponseCachingEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            ResponseCachingStartedEvent::NAME,
            StartResponseCachingEventSubscriber::class,
            'forceCookieConsentRendering',
        );
    }

    public function testForceCookieConsentRenderingCallsRendererForceRender(): void
    {
        $cookieConsentRenderer = $this->createMock(CookieConsentRenderer::class);

        $cookieConsentRenderer->expects($this->once())
            ->method('forceRender');

        $subscriber = new StartResponseCachingEventSubscriber($cookieConsentRenderer);
        $subscriber->forceCookieConsentRendering();
    }

    public function testForceCookieConsentRenderingWithRealRenderer(): void
    {
        // Use the real service from container to test integration
        /** @var CookieConsentRenderer $cookieConsentRenderer */
        $cookieConsentRenderer = self::getContainer()->get(CookieConsentRenderer::class);

        $subscriber = new StartResponseCachingEventSubscriber($cookieConsentRenderer);

        // Before forcing, the renderer should not be forced
        // We can't directly test the private property, but we can verify the method executes without error
        $subscriber->forceCookieConsentRendering();

        $this->expectNotToPerformAssertions();
    }

    public function testForceCookieConsentRenderingMultipleCalls(): void
    {
        $cookieConsentRenderer = $this->createMock(CookieConsentRenderer::class);

        // Should be called multiple times if the method is called multiple times
        $cookieConsentRenderer->expects($this->exactly(3))
            ->method('forceRender');

        $subscriber = new StartResponseCachingEventSubscriber($cookieConsentRenderer);

        // Call multiple times
        $subscriber->forceCookieConsentRendering();
        $subscriber->forceCookieConsentRendering();
        $subscriber->forceCookieConsentRendering();
    }
}
