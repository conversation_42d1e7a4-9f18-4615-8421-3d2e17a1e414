<?php

declare(strict_types=1);

namespace Tests\Integration\Infrastructure\UserInterface\Web\Component;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\Component\Yaml\Yaml;

final readonly class ComponentDataProvider
{
    /**
     * @param iterable<ComponentInterface> $components
     */
    public function __construct(
        private string $twigComponentsYamlFile,
        public iterable $components
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function getTwigComponentsConfig(): array
    {
        return Yaml::parseFile($this->twigComponentsYamlFile)['twig_component'] ?? [];
    }
}
