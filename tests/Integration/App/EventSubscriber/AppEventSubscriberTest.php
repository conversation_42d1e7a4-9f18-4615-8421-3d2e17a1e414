<?php

declare(strict_types=1);

namespace Tests\Integration\App\EventSubscriber;

use App\App\EventSubscriber\AppEventSubscriber;
use App\Template\Event\RenderTemplateHeadersEvent;
use Tests\Integration\AbstractBrandWebsitesIntegrationTestCase;

final class AppEventSubscriberTest extends AbstractBrandWebsitesIntegrationTestCase
{
    public function testEventRegistered(): void
    {
        $this->assertEventListenerInstanceIsRegistered(
            RenderTemplateHeadersEvent::NAME,
            AppEventSubscriber::class,
            'renderTemplateHeaders',
        );
    }
}
