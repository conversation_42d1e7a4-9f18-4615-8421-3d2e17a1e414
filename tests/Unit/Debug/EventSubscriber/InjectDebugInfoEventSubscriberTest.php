<?php

declare(strict_types=1);

namespace Tests\Unit\Debug\EventSubscriber;

use App\Debug\DebugInfoProviderInterface;
use App\Debug\EventSubscriber\InjectDebugInfoEventSubscriber;
use App\Debug\Helper\DebugHelper;
use App\Template\Event\RenderTemplateFootersEvent;
use PHPUnit\Framework\MockObject\MockObject;
use PHPUnit\Framework\TestCase;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\HttpKernel\Event\ResponseEvent;
use Symfony\Component\HttpKernel\HttpKernelInterface;
use Tests\Stub\Debug\Request\DebugRequestStub;
use Tests\Stub\Office\Request\OfficeRequestStub;
use Twig\Environment;

class InjectDebugInfoEventSubscriberTest extends TestCase
{
    private DebugRequestStub $debugRequestStub;

    private InjectDebugInfoEventSubscriber $subscriber;

    private DebugInfoProviderInterface & MockObject $debugInfoProviderMock;

    protected function setUp(): void
    {
        $officeRequestStub = new OfficeRequestStub();
        $this->debugRequestStub = new DebugRequestStub();
        $this->debugInfoProviderMock = $this->createMock(DebugInfoProviderInterface::class);

        $this->subscriber = new InjectDebugInfoEventSubscriber(
            $this->createMock(Environment::class),
            $officeRequestStub,
            $this->debugRequestStub,
            new DebugHelper($this->debugRequestStub, $officeRequestStub),
            [$this->debugInfoProviderMock],
        );
    }

    public function testInjectDebugInfoWhenDebugInfoDisabled(): void
    {
        $this->debugRequestStub->setDebugInfo(false);

        $event = new RenderTemplateFootersEvent();

        $this->subscriber->injectDebugInfo($event);

        self::assertEmpty($event->getItems());
    }

    public function testInjectDebugInfoWhenDebugInfoEnabled(): void
    {
        $this->debugRequestStub->setDebugInfo(true);

        $event = new RenderTemplateFootersEvent();

        $this->subscriber->injectDebugInfo($event);

        self::assertCount(1, $event->getItems());
        self::assertEquals(InjectDebugInfoEventSubscriber::DEBUG_INFO_HTML_PLACEHOLDER, $event->getItems()[0]);
    }

    public function testOnResponseWhenDebugInfoDisabled(): void
    {
        $this->debugRequestStub->setDebugInfo(false);

        $this->debugInfoProviderMock
            ->expects(self::never())
            ->method('getDebugInfo');

        $response = new Response(InjectDebugInfoEventSubscriber::DEBUG_INFO_HTML_PLACEHOLDER);
        $responseEvent = $this->createResponseEvent($response);

        $this->subscriber->onResponse($responseEvent);

        self::assertEquals(InjectDebugInfoEventSubscriber::DEBUG_INFO_HTML_PLACEHOLDER, $response->getContent());
    }

    private function createResponseEvent(Response $response): ResponseEvent
    {
        return new ResponseEvent(
            $this->createMock(HttpKernelInterface::class),
            new Request(),
            HttpKernelInterface::MAIN_REQUEST,
            $response,
        );
    }
}
