<?php

declare(strict_types=1);

namespace Tests\Unit\Application\Command\StyleId\UpdateVbbStylesForBrand;

use Application\Command\StyleId\UpdateVbbStylesForBrand\UpdateVbbStylesForBrandCommand;
use Application\Command\StyleId\UpdateVbbStylesForBrand\UpdateVbbStylesForBrandHandler;
use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\VbbStyleId\VbbStyleIdFactory;
use PHPUnit\Framework\TestCase;
use Tests\Stub\Domain\Brand\BrandRepositoryStub;
use Tests\Stub\Domain\Brand\BrandStubBuilder;
use Tests\Stub\Domain\VbbStyleId\VbbStyleIdRepositoryStub;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;

class UpdateVbbStylesForBrandHandlerTest extends TestCase
{
    private BrandRepositoryStub $brandRepositoryStub;

    private VbbStyleIdRepositoryStub $vbbStyleIdRepositoryStub;

    private VbbStyleIdFactory $vbbStyleIdFactory;

    private UpdateVbbStylesForBrandHandler $handler;

    protected function setUp(): void
    {
        $brandStubBuilder = new BrandStubBuilder();
        $brand = $brandStubBuilder->setSlug('breanto')->create();
        $this->brandRepositoryStub = new BrandRepositoryStub();
        $this->brandRepositoryStub->store($brand);
        $this->vbbStyleIdRepositoryStub = new VbbStyleIdRepositoryStub();
        $this->vbbStyleIdFactory = new VbbStyleIdFactory();
        $commandBus = $this->createMock(CommandBusInterface::class);
        $dateTimeFactory = new DateTimeFactory();

        $this->handler = new UpdateVbbStylesForBrandHandler(
            $this->brandRepositoryStub,
            $this->vbbStyleIdRepositoryStub,
            $this->vbbStyleIdFactory,
            $commandBus,
            $dateTimeFactory
        );
    }

    public function testAddNewStyleIdsStub(): void
    {
        $brand = $this->brandRepositoryStub->findOneBySlug('breanto');
        $vbbStyleId = $this->vbbStyleIdRepositoryStub->findBySlug($brand->slug);
        $command = new UpdateVbbStylesForBrandCommand($brand->slug, ['1234567890', '1987654321']);
        self::assertSame([], $vbbStyleId);

        $this->handler->handle($command);

        $vbbStyleIds = $this->vbbStyleIdRepositoryStub->findBySlug($brand->slug);
        self::assertCount(2, $vbbStyleIds);
    }

    public function testRemoveOldStyleIds(): void
    {
        $brand = $this->brandRepositoryStub->findOneBySlug('breanto');
        $vbbStyle = $this->vbbStyleIdFactory->create(
            AdSenseStyleId::fromStyleId('1987654321'),
            $brand
        );
        $this->vbbStyleIdRepositoryStub->store($vbbStyle);

        $vbbStyle = $this->vbbStyleIdRepositoryStub->findOneByStyleId(
            AdSenseStyleId::fromStyleId('1987654321')
        );

        self::assertNotNull($vbbStyle);

        $command = new UpdateVbbStylesForBrandCommand($brand->slug, []);

        $this->handler->handle($command);

        $vbbStyles = $this->vbbStyleIdRepositoryStub->findBySlug($brand->slug);
        self::assertEmpty($vbbStyles);
    }

    public function testDoesNotAddOrRemoveWhenStyleIdsAreUnchanged(): void
    {
        $brand = $this->brandRepositoryStub->findOneBySlug('breanto');
        $vbbStyle = $this->vbbStyleIdFactory->create(
            AdSenseStyleId::fromStyleId('1234567890'),
            $brand
        );
        $this->vbbStyleIdRepositoryStub->store($vbbStyle);
        self::assertNotEmpty($this->vbbStyleIdRepositoryStub->findBySlug($brand->slug));
        $command = new UpdateVbbStylesForBrandCommand($brand->slug, [(string)$vbbStyle->styleId]);

        $this->handler->handle($command);

        self::assertNotSame([], $this->vbbStyleIdRepositoryStub->findBySlug($brand->slug));
    }
}
