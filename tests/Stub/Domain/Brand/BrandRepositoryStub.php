<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\Brand;

use Domain\Brand\Brand;
use Domain\Brand\BrandRepositoryInterface;
use Domain\Brand\Exception\BrandNotFoundException;

readonly class BrandRepositoryStub implements BrandRepositoryInterface
{
    /** @var \ArrayObject<string, Brand> */
    private \ArrayObject $brands;

    public function __construct()
    {
        $this->brands = new \ArrayObject();
    }

    public function findOneBySlug(string $slug): Brand
    {
        /** @var Brand|null $brand */
        $brand = $this->brands->offsetGet($slug);

        if ($brand !== null) {
            return $brand;
        }

        throw BrandNotFoundException::createForSlug($slug);
    }

    public function findOneBySlugMayReturnNull(string $slug): ?Brand
    {
        return $this->brands->offsetGet($slug);
    }

    /**
     * @inheritDoc
     */
    public function getAllSlugs(): array
    {
        $slugs = [];

        foreach ($this->brands as $brand) {
            $slugs[$brand->id] = $brand->slug;
        }

        return $slugs;
    }

    /**
     * @inheritDoc
     */
    public function getAllProjects(): array
    {
        $projects = [];

        foreach ($this->brands as $brand) {
            $projects[] = $brand->project;
        }

        return array_unique($projects);
    }

    public function findAll(?string $sort = null, ?string $sortDirection = null): \Generator
    {
        foreach ($this->brands as $brand) {
            yield $brand;
        }
    }

    public function findWithDisplaySearchEnabled(): \Generator
    {
        foreach ($this->brands as $brand) {
            if ($brand->displaySearchRelated?->enabled === true) {
                yield $brand;
            }
        }
    }

    public function findWithMonetization(): \Generator
    {
        foreach ($this->brands as $brand) {
            if ($brand->monetization !== null) {
                yield $brand;
            }
        }
    }

    public function findForOrganicResults(): \Generator
    {
        foreach ($this->brands as $brand) {
            if ($brand->contentPage === null) {
                continue;
            }

            $isBrandForOrganicResults = $brand->contentPage->enabled &&
                                        $brand->contentPage->useBrandForOrganicResults &&
                                        $brand->contentPage->organicResultRoute !== null;

            if ($isBrandForOrganicResults) {
                yield $brand;
            }
        }
    }

    public function findOneActiveWithoutPartnerSlug(): ?Brand
    {
        foreach ($this->brands as $brand) {
            if ($brand->active && $brand->partnerSlug === null) {
                return $brand;
            }
        }

        return null;
    }

    /**
     * @param array<Brand> $brands
     */
    public function storeBrands(array $brands): void
    {
        foreach ($brands as $brand) {
            $this->store($brand);
        }
    }

    public function store(Brand $brand): void
    {
        $this->brands->offsetSet($brand->slug, $brand);
        $this->brands->ksort();
    }

    public function delete(Brand $brand): void
    {
        $this->brands->offsetUnset($brand->slug);
    }
}
