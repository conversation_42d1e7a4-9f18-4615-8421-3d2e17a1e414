<?php

declare(strict_types=1);

namespace Tests\Stub\Domain\Brand;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\AntelopeBrand\AntelopeBrandStatus;
use Domain\Article\Article;
use Domain\Author\Author;
use Domain\Brand\Brand;
use Domain\Brand\ContractType\AdSenseContractType;
use Domain\Cheq\Cheq;
use Domain\ContentPage\ContentPage;
use Domain\ContentPageHome\ContentPageHome;
use Domain\ContentPageHome\ContentPageHomeType;
use Domain\ContentSearch\ContentSearch;
use Domain\DisplaySearch\DisplaySearch;
use Domain\DisplaySearchRelated\DisplaySearchRelated;
use Domain\GooglePublisherTag\GooglePublisherTag;
use Domain\GoogleTagManager\GoogleTagManager;
use Domain\ImageSearch\ImageSearch;
use Domain\InfoPages\InfoPages;
use Domain\JavaScriptRelatedTerms\JavaScriptRelatedTerms;
use Domain\JsonTemplate\JsonTemplate;
use Domain\MicrosoftSearch\MicrosoftSearch;
use Domain\MicrosoftSearchRelated\MicrosoftSearchRelated;
use Domain\Monetization\Monetization;
use Domain\NewsSearch\NewsSearch;
use Domain\OneTrust\OneTrust;
use Domain\PageviewConversion\PageviewConversion;
use Domain\Search\Search;
use Domain\SearchRoute\SearchRoute;
use Domain\SpamClickDetect\SpamClickDetect;
use Domain\Tracking\Tracking;
use Domain\WebSearch\WebSearch;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;
use Visymo\Shared\Infrastructure\Stub\Domain\DateTime\DateTimeFactoryStub;

class BrandStubBuilder
{
    private DateTimeFactoryStub $dateTimeFactoryStub;

    private int $id = 1;

    private string $slug = 'slug';

    private ?string $partnerSlug = null;

    private ?AdSenseContractType $adSenseContractType = null;

    private \DateTime $lastImportedAt;

    private ?\DateTime $lastPushedAt = null;

    private AntelopeBrandStatus $status = AntelopeBrandStatus::SUCCESS;

    private bool $hasBingAdsApproval = false;

    private bool $hasGoogleAdSenseApproval = false;

    private bool $active = false;

    private ?Article $article = null;

    private ?Cheq $cheq = null;

    private ?ContentPage $contentPage = null;

    private ?ContentPageHome $contentPageHome = null;

    private ?ContentSearch $contentSearch = null;

    private ?DisplaySearch $displaySearch = null;

    private ?DisplaySearchRelated $displaySearchRelated = null;

    private ?GooglePublisherTag $googlePublisherTag = null;

    private ?GoogleTagManager $googleTagManager = null;

    private ?ImageSearch $imageSearch = null;

    private ?InfoPages $infoPages = null;

    private ?JsonTemplate $jsonTemplate = null;

    private ?JavaScriptRelatedTerms $javaScriptRelatedTerms = null;

    private ?MicrosoftSearch $microsoftSearch = null;

    private ?MicrosoftSearchRelated $microsoftSearchRelated = null;

    private ?Monetization $monetization = null;

    private ?NewsSearch $newsSearch = null;

    private ?OneTrust $oneTrust = null;

    private ?PageviewConversion $pageviewConversion = null;

    private ?Search $search = null;

    private ?SpamClickDetect $spamClickDetect = null;

    private ?Tracking $tracking = null;

    private ?WebSearch $webSearch = null;

    public function __construct(
        ?DateTimeFactoryStub $dateTimeFactoryStub = null
    )
    {
        $this->dateTimeFactoryStub = $dateTimeFactoryStub ?? new DateTimeFactoryStub();
        $this->reset();
    }

    public function enableAllModules(): self
    {
        return $this
            ->setArticle(
                new Article(true)
            )
            ->setCheq(
                new Cheq(true)
            )
            ->setContentPage(
                new ContentPage(
                    true,
                    $this->slug,
                    new Author(
                        Author::EDITORIAL_TEAM,
                        Author::EDITORIAL_TEAM
                    ),
                    false,
                    null
                )
            )
            ->setContentPageHome(
                new ContentPageHome(
                    true,
                    ContentPageHomeType::HOME_2,
                    SearchRoute::DISPLAY_SEARCH_RELATED_WEB
                )
            )
            ->setContentSearch(
                new ContentSearch(true)
            )
            ->setDisplaySearch(
                new DisplaySearch(true)
            )
            ->setDisplaySearchRelated(
                new DisplaySearchRelated(
                    true,
                    true,
                    AdSenseStyleId::fromStyleId('1234567890'),
                    AdSenseStyleId::fromStyleId('1234567890'),
                    AdSenseStyleId::fromStyleId('1234567890'),
                    AdSenseStyleId::fromStyleId('1234567890')
                )
            )
            ->setGooglePublisherTag(
                new GooglePublisherTag(true, 'googlePublisherTagId')
            )
            ->setGoogleTagManager(
                new GoogleTagManager(
                    true,
                    'googleTagManagerId',
                    [SearchRoute::WEB_SEARCH]
                )
            )
            ->setJavaScriptRelatedTerms(
                new JavaScriptRelatedTerms(
                    true,
                    AdSenseStyleId::fromStyleId('1234567890'),
                    true,
                    SearchRoute::DISPLAY_SEARCH_RELATED,
                    true,
                    SearchRoute::WEB_SEARCH
                )
            )
            ->setMicrosoftSearch(
                new MicrosoftSearch(true)
            )
            ->setMicrosoftSearchRelated(
                new MicrosoftSearchRelated(
                    true,
                    AdSenseStyleId::fromStyleId('1234567890'),
                    AdSenseStyleId::fromStyleId('1234567891')
                )
            )
            ->setMonetization(
                new Monetization(
                    true,
                    true,
                    true,
                    \DateTimeImmutable::createFromMutable($this->dateTimeFactoryStub->createNow())
                )
            )
            ->setOneTrust(
                new OneTrust(true, 'domainScriptId')
            )
            ->setPageviewConversion(
                new PageviewConversion(true, [SearchRoute::DISPLAY_SEARCH_RELATED])
            )
            ->setSearch(
                new Search(
                    true,
                    false,
                    AdSenseStyleId::fromStyleId('1234567890'),
                    AdSenseStyleId::fromStyleId('1234567891')
                )
            )
            ->setSpamClickDetect(
                new SpamClickDetect(true, [SearchRoute::DISPLAY_SEARCH_RELATED_WEB])
            )
            ->setTracking(
                new Tracking(true)
            )
            ->setWebSearch(
                new WebSearch(
                    true,
                    AdSenseStyleId::fromStyleId('1234567890'),
                    AdSenseStyleId::fromStyleId('1234567891')
                )
            );
    }

    public function setId(int $id): self
    {
        $this->id = $id;

        return $this;
    }

    public function setSlug(string $slug): self
    {
        $this->slug = $slug;

        return $this;
    }

    public function setActive(bool $active): self
    {
        $this->active = $active;

        return $this;
    }

    public function setLastImportedAt(\DateTime $lastImportedAt): self
    {
        $this->lastImportedAt = $lastImportedAt;

        return $this;
    }

    public function setLastPushedAt(\DateTime $lastPushedAt): self
    {
        $this->lastPushedAt = $lastPushedAt;

        return $this;
    }

    public function setStatus(AntelopeBrandStatus $status): self
    {
        $this->status = $status;

        return $this;
    }

    public function setHasGoogleAdSenseApproval(bool $hasGoogleAdSenseApproval): self
    {
        $this->hasGoogleAdSenseApproval = $hasGoogleAdSenseApproval;

        return $this;
    }

    public function setArticle(?Article $article): self
    {
        $this->article = $article;

        return $this;
    }

    public function setCheq(?Cheq $cheq): self
    {
        $this->cheq = $cheq;

        return $this;
    }

    public function setContentPage(?ContentPage $contentPage): self
    {
        $this->contentPage = $contentPage;

        return $this;
    }

    public function setContentPageHome(?ContentPageHome $contentPageHome): self
    {
        $this->contentPageHome = $contentPageHome;

        return $this;
    }

    public function setContentSearch(?ContentSearch $contentSearch): self
    {
        $this->contentSearch = $contentSearch;

        return $this;
    }

    public function setAdSenseContractType(?AdSenseContractType $adSenseContractType): self
    {
        $this->adSenseContractType = $adSenseContractType;

        return $this;
    }

    public function setDisplaySearch(?DisplaySearch $displaySearch): self
    {
        $this->displaySearch = $displaySearch;

        return $this;
    }

    public function setDisplaySearchRelated(?DisplaySearchRelated $displaySearchRelated): self
    {
        $this->displaySearchRelated = $displaySearchRelated;

        return $this;
    }

    public function setGooglePublisherTag(?GooglePublisherTag $googlePublisherTag): self
    {
        $this->googlePublisherTag = $googlePublisherTag;

        return $this;
    }

    public function setGoogleTagManager(?GoogleTagManager $googleTagManager): self
    {
        $this->googleTagManager = $googleTagManager;

        return $this;
    }

    public function setImageSearch(?ImageSearch $imageSearch): self
    {
        $this->imageSearch = $imageSearch;

        return $this;
    }

    public function setInfoPages(?InfoPages $infoPages): self
    {
        $this->infoPages = $infoPages;

        return $this;
    }

    public function setJavaScriptRelatedTerms(?JavaScriptRelatedTerms $javaScriptRelatedTerms): self
    {
        $this->javaScriptRelatedTerms = $javaScriptRelatedTerms;

        return $this;
    }

    public function setJsonTemplate(?JsonTemplate $jsonTemplate): self
    {
        $this->jsonTemplate = $jsonTemplate;

        return $this;
    }

    public function setMicrosoftSearch(?MicrosoftSearch $microsoftSearch): self
    {
        $this->microsoftSearch = $microsoftSearch;

        return $this;
    }

    public function setMicrosoftSearchRelated(?MicrosoftSearchRelated $microsoftSearchRelated): self
    {
        $this->microsoftSearchRelated = $microsoftSearchRelated;

        return $this;
    }

    public function setMonetization(?Monetization $monetization): self
    {
        $this->monetization = $monetization;

        return $this;
    }

    public function setNewsSearch(?NewsSearch $newsSearch): self
    {
        $this->newsSearch = $newsSearch;

        return $this;
    }

    public function setOneTrust(?OneTrust $oneTrust): self
    {
        $this->oneTrust = $oneTrust;

        return $this;
    }

    public function setPageviewConversion(?PageviewConversion $pageviewConversion): self
    {
        $this->pageviewConversion = $pageviewConversion;

        return $this;
    }

    public function setPartnerSlug(?string $partnerSlug): self
    {
        $this->partnerSlug = $partnerSlug;

        return $this;
    }

    public function setSearch(?Search $search): self
    {
        $this->search = $search;

        return $this;
    }

    public function setSpamClickDetect(?SpamClickDetect $spamClickDetect): self
    {
        $this->spamClickDetect = $spamClickDetect;

        return $this;
    }

    public function setTracking(?Tracking $tracking): self
    {
        $this->tracking = $tracking;

        return $this;
    }

    public function setWebSearch(?WebSearch $webSearch): self
    {
        $this->webSearch = $webSearch;

        return $this;
    }

    public function reset(): self
    {
        $dateTime = $this->dateTimeFactoryStub->create('2024-01-01 00:00:00', TimezoneEnum::UTC);

        $this->id = 1;
        $this->slug = 'slug';
        $this->partnerSlug = null;
        $this->adSenseContractType = null;
        $this->lastImportedAt = $dateTime;
        $this->lastPushedAt = null;
        $this->status = AntelopeBrandStatus::SUCCESS;
        $this->hasBingAdsApproval = false;
        $this->hasGoogleAdSenseApproval = false;
        $this->active = false;
        $this->article = null;
        $this->cheq = null;
        $this->contentPage = null;
        $this->contentPageHome = null;
        $this->contentSearch = null;
        $this->displaySearch = null;
        $this->displaySearchRelated = null;
        $this->googlePublisherTag = null;
        $this->googleTagManager = null;
        $this->imageSearch = null;
        $this->infoPages = null;
        $this->javaScriptRelatedTerms = null;
        $this->jsonTemplate = null;
        $this->microsoftSearch = null;
        $this->microsoftSearchRelated = null;
        $this->monetization = null;
        $this->newsSearch = null;
        $this->oneTrust = null;
        $this->pageviewConversion = null;
        $this->search = null;
        $this->spamClickDetect = null;
        $this->tracking = null;
        $this->webSearch = null;

        return $this;
    }

    public function create(): Brand
    {
        $brand = new Brand(
            slug                    : $this->slug,
            partnerSlug             : $this->partnerSlug,
            adSenseContractType     : $this->adSenseContractType,
            lastImportedAt          : $this->lastImportedAt,
            lastPushedAt            : $this->lastPushedAt,
            status                  : $this->status,
            hasBingAdsApproval      : $this->hasBingAdsApproval,
            hasGoogleAdSenseApproval: $this->hasGoogleAdSenseApproval,
            active                  : $this->active,
            article                 : $this->article,
            cheq                    : $this->cheq,
            contentPage             : $this->contentPage,
            contentPageHome         : $this->contentPageHome,
            contentSearch           : $this->contentSearch,
            displaySearch           : $this->displaySearch,
            displaySearchRelated    : $this->displaySearchRelated,
            googlePublisherTag      : $this->googlePublisherTag,
            googleTagManager        : $this->googleTagManager,
            imageSearch             : $this->imageSearch,
            infoPages               : $this->infoPages,
            javaScriptRelatedTerms  : $this->javaScriptRelatedTerms,
            jsonTemplate            : $this->jsonTemplate,
            microsoftSearch         : $this->microsoftSearch,
            microsoftSearchRelated  : $this->microsoftSearchRelated,
            monetization            : $this->monetization,
            newsSearch              : $this->newsSearch,
            oneTrust                : $this->oneTrust,
            pageviewConversion      : $this->pageviewConversion,
            search                  : $this->search,
            spamClickDetect         : $this->spamClickDetect,
            tracking                : $this->tracking,
            webSearch               : $this->webSearch,
        );
        $brand->id = $this->id;
        $this->id++;

        return $brand;
    }
}
