<?php

declare(strict_types=1);

namespace Tests\Stub\Infrastructure\Persistence\Database;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\AntelopeBrand\AntelopeBrandFactory;
use Domain\AntelopeBrand\AntelopeBrandStatus;
use Domain\BrandAssets\BrandAssetsFactory;
use Domain\BrandModuleStatistic\BrandModuleStatistic;
use Domain\BrandModuleStatistic\Data\BrandModuleStatisticDataResult;
use Domain\BrandModuleStatistic\Data\BrandModuleStatisticDataResults;
use Domain\ContentPage\ContentPage;
use Domain\DisplaySearchRelated\DisplaySearchRelated;
use Domain\Generic\UtcDate;
use Domain\InfoPages\InfoPages;
use Domain\JsonTemplate\JsonTemplate;
use Domain\MicrosoftSearchRelated\MicrosoftSearchRelated;
use Domain\SearchRoute\SearchRoute;
use Domain\TrademarkInfringement\TrademarkInfringementMatchType;
use Domain\VbbStyleId\VbbStyleId;
use Domain\WebSearch\WebSearch;
use Symfony\Component\DependencyInjection\ContainerInterface;
use Tests\Stub\Domain\AntelopeBrand\AntelopeBrandRepositoryStub;
use Tests\Stub\Domain\Author\AuthorRepositoryStub;
use Tests\Stub\Domain\Author\AuthorStubBuilder;
use Tests\Stub\Domain\Brand\BrandRepositoryStub;
use Tests\Stub\Domain\Brand\BrandStubBuilder;
use Tests\Stub\Domain\BrandAssets\BrandAssetsRepositoryStub;
use Tests\Stub\Domain\BrandModuleStatistic\BrandModuleStatisticRepositoryStub;
use Tests\Stub\Domain\Scraper\ScraperBatch\ScraperBatchRepositoryStub;
use Tests\Stub\Domain\Scraper\ScraperBatchUrl\ScraperBatchUrlRepositoryStub;
use Tests\Stub\Domain\Scraper\ScraperRepositoryStub;
use Tests\Stub\Domain\Scraper\ScraperStubBuilder;
use Tests\Stub\Domain\TrademarkInfringement\TrademarkInfringementRepositoryStub;
use Tests\Stub\Domain\TrademarkInfringement\TrademarkInfringementStubBuilder;
use Tests\Stub\Domain\VbbStyleId\VbbStyleIdRepositoryStub;
use Visymo\Filesystem\SerializedFile\Iterator\SerializedFileIterator;
use Visymo\Filesystem\SerializedFile\Type\NativeJsonFileFactory;

final readonly class DatabaseStub
{
    public AntelopeBrandRepositoryStub $antelopeBrandRepositoryStub;

    public AuthorRepositoryStub $authorRepositoryStub;

    public BrandAssetsRepositoryStub $brandAssetsRepositoryStub;

    public BrandModuleStatisticRepositoryStub $brandModuleStatisticRepositoryStub;

    public BrandRepositoryStub $brandRepositoryStub;

    public ScraperRepositoryStub $scraperRepositoryStub;

    public ScraperBatchRepositoryStub $scraperBatchRepositoryStub;

    public ScraperBatchUrlRepositoryStub $scraperBatchUrlRepositoryStub;

    public TrademarkInfringementRepositoryStub $trademarkInfringementRepositoryStub;

    public VbbStyleIdRepositoryStub $vbbStyleIdRepositoryStub;

    public function __construct(
        private ContainerInterface $container
    )
    {
        $repositoryStubs = [
            $this->antelopeBrandRepositoryStub = new AntelopeBrandRepositoryStub(),
            $this->authorRepositoryStub = new AuthorRepositoryStub(),
            $this->brandAssetsRepositoryStub = new BrandAssetsRepositoryStub(),
            $this->brandModuleStatisticRepositoryStub = new BrandModuleStatisticRepositoryStub(),
            $this->brandRepositoryStub = new BrandRepositoryStub(),
            $this->scraperRepositoryStub = new ScraperRepositoryStub(),
            $this->scraperBatchRepositoryStub = new ScraperBatchRepositoryStub(),
            $this->scraperBatchUrlRepositoryStub = new ScraperBatchUrlRepositoryStub(),
            $this->trademarkInfringementRepositoryStub = new TrademarkInfringementRepositoryStub(),
            $this->vbbStyleIdRepositoryStub = new VbbStyleIdRepositoryStub(),
        ];

        foreach ($repositoryStubs as $repositoryStub) {
            $this->addRepositoryStubToContainer($repositoryStub);
        }
    }

    public function build(): void
    {
        $this->configureBrands();
        $this->configureAntelopeBrands();
        $this->configureBrandAssets();
        $this->configureVbbStyleIds();
        $this->configureBrandModuleStatistics();
        $this->configureAuthors();
        $this->configureTrademarkInfringements();
        $this->configureScrapers();
    }

    private function addRepositoryStubToContainer(object $repositoryStub): void
    {
        $reflectionClass = new \ReflectionClass($repositoryStub);
        $repositoryInterfaceName = str_replace('Stub', 'Interface', $reflectionClass->getShortName());
        $repositoryInterfaceName = sprintf('\%s', $repositoryInterfaceName);

        foreach ($reflectionClass->getInterfaceNames() as $interfaceNamespace) {
            if (str_ends_with($interfaceNamespace, $repositoryInterfaceName)) {
                $this->container->set($interfaceNamespace, $repositoryStub);

                return;
            }
        }

        throw new \RuntimeException(
            sprintf('Could not find repository interface %s', $repositoryInterfaceName)
        );
    }

    private function configureAntelopeBrands(): void
    {
        /** @var NativeJsonFileFactory $fileFactory */
        $fileFactory = $this->container->get(NativeJsonFileFactory::class);
        $fileIterator = new SerializedFileIterator(
            globPattern          : __DIR__.'/_antelope_brand_config/*.json',
            serializedFileFactory: $fileFactory
        );

        /** @var AntelopeBrandFactory $antelopeBrandFactory */
        $antelopeBrandFactory = $this->container->get(AntelopeBrandFactory::class);

        foreach ($fileIterator->iterate() as $file) {
            $antelopeBrand = $antelopeBrandFactory->create(
                config             : $file->getContents(),
                antelopeBrandStatus: AntelopeBrandStatus::SUCCESS
            );
            $brand = $this->brandRepositoryStub->findOneBySlug($antelopeBrand->getSlug());

            if ($brand !== null) {
                $antelopeBrand->brand = $brand;
            }

            $this->antelopeBrandRepositoryStub->store($antelopeBrand);
        }
    }

    private function configureBrands(): void
    {
        $brandStubBuilder = new BrandStubBuilder();

        $this->brandRepositoryStub->store(
            $brandStubBuilder
                ->reset()
                ->setActive(true)
                ->setSlug('breanto')
                ->setWebSearch(
                    new WebSearch(
                        enabled       : true,
                        styleIdDesktop: AdSenseStyleId::fromStyleId('2234567890'),
                        styleIdMobile : AdSenseStyleId::fromStyleId('2234567890')
                    )
                )
                ->create()
        );
        $this->brandRepositoryStub->store(
            $brandStubBuilder
                ->reset()
                ->setActive(true)
                ->setSlug('visymo')
                ->setContentPage(
                    new ContentPage(
                        enabled                  : true,
                        collection               : 'visymo',
                        author                   : null,
                        useBrandForOrganicResults: true,
                        organicResultRoute       : SearchRoute::DISPLAY_SEARCH_RELATED
                    )
                )
                ->create()
        );
        $this->brandRepositoryStub->store(
            $brandStubBuilder
                ->reset()
                ->setActive(true)
                ->setSlug('prorateer')
                ->setDisplaySearchRelated(
                    new DisplaySearchRelated(
                        enabled               : true,
                        relatedFallbackEnabled: true,
                        styleIdDesktop        : AdSenseStyleId::fromStyleId('1234567890'),
                        styleIdMobile         : AdSenseStyleId::fromStyleId('1234567890'),
                        webStyleIdDesktop     : AdSenseStyleId::fromStyleId('1234567895'),
                        webStyleIdMobile      : AdSenseStyleId::fromStyleId('1234567898')
                    )
                )
                ->setContentPage(
                    new ContentPage(
                        enabled                  : true,
                        collection               : 'prorateer',
                        author                   : null,
                        useBrandForOrganicResults: true,
                        organicResultRoute       : SearchRoute::MICROSOFT_SEARCH
                    )
                )
                ->setMicrosoftSearchRelated(
                    new MicrosoftSearchRelated(
                        enabled       : false,
                        styleIdDesktop: AdSenseStyleId::fromStyleId('4554567890'),
                        styleIdMobile : AdSenseStyleId::fromStyleId('4554567892')
                    )
                )
                ->create()
        );
        $this->brandRepositoryStub->store(
            $brandStubBuilder
                ->reset()
                ->setActive(true)
                ->setSlug('zapmeta')
                ->setDisplaySearchRelated(
                    new DisplaySearchRelated(
                        enabled               : true,
                        relatedFallbackEnabled: true,
                        styleIdDesktop        : AdSenseStyleId::fromStyleId('1234567890'),
                        styleIdMobile         : AdSenseStyleId::fromStyleId('1234567891'),
                        webStyleIdDesktop     : AdSenseStyleId::fromStyleId('1234567893'),
                        webStyleIdMobile      : AdSenseStyleId::fromStyleId('1234567898')
                    )
                )
                ->create()
        );
        $this->brandRepositoryStub->store(
            $brandStubBuilder
                ->reset()
                ->setActive(true)
                ->setSlug('seekweb')
                ->setStatus(AntelopeBrandStatus::SUCCESS)
                ->enableAllModules()
                ->setInfoPages(
                    new InfoPages(
                        false,
                        false,
                        'search'
                    )
                )
                ->setJsonTemplate(
                    new JsonTemplate(
                        'dark',
                        'dsr_ea,ws_ea',
                    )
                )
                ->create()
        );
    }

    private function configureBrandAssets(): void
    {
        /** @var NativeJsonFileFactory $fileFactory */
        $fileFactory = $this->container->get(NativeJsonFileFactory::class);
        $fileIterator = new SerializedFileIterator(
            globPattern          : __DIR__.'/_brand_assets/*.json',
            serializedFileFactory: $fileFactory
        );

        /** @var BrandAssetsFactory $brandAssetsFactory */
        $brandAssetsFactory = $this->container->get(BrandAssetsFactory::class);

        foreach ($fileIterator->iterate() as $file) {
            $slug = $file->getBaseName();
            $brand = $this->brandRepositoryStub->findOneBySlug($slug);

            if ($brand !== null) {
                $brandAssets = $brandAssetsFactory->create(
                    brand : $brand,
                    config: $file->getContents(),
                );
                $this->brandAssetsRepositoryStub->store($brandAssets);
            }
        }
    }

    private function configureBrandModuleStatistics(): void
    {
        // Zapmeta
        $data = new BrandModuleStatisticDataResults(
            [
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/dsr',
                    count       : 1549,
                    isAdBot     : false
                ),
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/dsrw',
                    count       : 543,
                    isAdBot     : false
                ),
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/dsr',
                    count       : 544149,
                    isAdBot     : true
                ),
            ]
        );
        $brandModuleStatistic = new BrandModuleStatistic(
            date: new UtcDate('2024-11-01'),
            data: $data
        );
        $brand = $this->brandRepositoryStub->findOneBySlug('zapmeta');

        if ($brand !== null) {
            $brandModuleStatistic->brand = $brand;
            $this->brandModuleStatisticRepositoryStub->store($brandModuleStatistic);
        }

        // Breanto
        $data = new BrandModuleStatisticDataResults(
            [
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/ws',
                    count       : 29292,
                    isAdBot     : false
                ),
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/wsa',
                    count       : 145,
                    isAdBot     : false
                ),
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/ws',
                    count       : 8755465,
                    isAdBot     : true
                ),
            ]
        );
        $brandModuleStatistic = new BrandModuleStatistic(
            date: new UtcDate('2024-11-02'),
            data: $data
        );
        $brand = $this->brandRepositoryStub->findOneBySlug('breanto');

        if ($brand !== null) {
            $brandModuleStatistic->brand = $brand;
            $this->brandModuleStatisticRepositoryStub->store($brandModuleStatistic);
        }

        // Seekweb
        $data = new BrandModuleStatisticDataResults(
            [
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/ws',
                    count       : 445,
                    isAdBot     : false
                ),
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/wsa',
                    count       : 12,
                    isAdBot     : false
                ),
                new BrandModuleStatisticDataResult(
                    wildcardPath: '/ws',
                    count       : 98767,
                    isAdBot     : true
                ),
            ]
        );
        $brandModuleStatistic = new BrandModuleStatistic(
            date: new UtcDate('2024-11-01'),
            data: $data
        );
        $brand = $this->brandRepositoryStub->findOneBySlug('seekweb');

        if ($brand !== null) {
            $brandModuleStatistic->brand = $brand;
            $this->brandModuleStatisticRepositoryStub->store($brandModuleStatistic);
        }
    }

    private function configureVbbStyleIds(): void
    {
        $brand = $this->brandRepositoryStub->findOneBySlug('prorateer');

        if ($brand !== null) {
            $this->vbbStyleIdRepositoryStub->store(
                new VbbStyleId(
                    styleId       : AdSenseStyleId::fromStyleId('1234567890'),
                    brand         : $brand,
                    lastImportedAt: new UtcDate('2024-11-01'),
                    lastPushedAt  : null
                )
            );
        }
    }

    private function configureAuthors(): void
    {
        $authorStubBuilder = new AuthorStubBuilder();

        $this->authorRepositoryStub->store(
            $authorStubBuilder
                ->reset()
                ->setSlug('editorial_team')
                ->setName('Editorial Team')
                ->create()
        );
    }

    private function configureTrademarkInfringements(): void
    {
        $trademarkInfringementStubBuilder = (new TrademarkInfringementStubBuilder())
            ->setLocales(['de_DE', 'en_US'])
            ->setCreatedAt('2024-11-05');
        $this->trademarkInfringementRepositoryStub->store($trademarkInfringementStubBuilder->create());

        $trademarkInfringementStubBuilder
            ->reset()
            ->setId(2)
            ->setQuery('pizza hawaii')
            ->setMatchType(TrademarkInfringementMatchType::PHRASE)
            ->setCreatedAt('2025-01-05');
        $this->trademarkInfringementRepositoryStub->store($trademarkInfringementStubBuilder->create());
    }

    private function configureScrapers(): void
    {
        $scraperStubBuilder = new ScraperStubBuilder();

        $this->scraperRepositoryStub->store(
            $scraperStubBuilder->create()
        );
    }
}
