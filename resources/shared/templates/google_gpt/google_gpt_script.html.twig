{# @var pub_ads_service \App\GooglePublisherTag\PubAdsService #}
{# @var display_conversion_url string #}
{# @var requested_slots int #}
<script async="async" src="https://securepubads.g.doubleclick.net/tag/js/gpt.js"></script>
{{ entry_javascript('GoogleGpt') }}
<script>
    window.googletag = window.googletag || {cmd: []};

    appReady.push(function GoogleGptScript() {
        new GoogleGptStatisticsResult({{ requested_slots }});

        new GoogleGptDisplayConversionLogger(
            "{{ display_conversion_url|raw }}",
            "{{ persistent_path_query_string({}, 'conversion_tracking') }}"
        );

        googletag.cmd.push(function() {
            {# Define page-level settings #}
            {% if pub_ads_service.enableSingleRequest %}
            googletag.pubads().enableSingleRequest();
            {% endif %}
            {% if pub_ads_service.collapseEmptyDivs %}
            googletag.pubads().collapseEmptyDivs();
            {% endif %}
            {% for key, values in pub_ads_service.targeting %}
            googletag.pubads().setTargeting('{{ key }}', {{ values|json_encode|raw }});
            {% endfor %}
            {#  Enable Services #}
            googletag.enableServices();
        });
    });
</script>
