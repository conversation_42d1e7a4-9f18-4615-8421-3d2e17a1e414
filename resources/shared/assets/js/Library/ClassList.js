var ClassList = {
    get: function (element) {
        var value = element.getAttribute('class');

        return value !== null ? value : '';
    },
    add: function (element, value) {
        if (!element) {
            return;
        }

        this.remove(element, value);

        var newValue = this.get(element) + ' ' + value;
        newValue = Helper.trim(newValue);

        element.setAttribute('class', newValue);
    },
    toggle: function (element, value, add) {
        if (!element) {
            return;
        }

        if (typeof add === 'boolean') {
            add ? this.add(element, value) : this.remove(element, value);
        } else if (!this.has(element, value)) {
            this.add(element, value);
        } else {
            this.remove(element, value);
        }
    },
    remove: function (element, value) {
        if (!element) {
            return;
        }

        var newValue = ' ' + this.get(element) + ' ';

        newValue = newValue.replace(' ' + value + ' ', ' ');
        newValue = Helper.trim(newValue);

        element.setAttribute('class', newValue);
    },
    has: function (element, value) {
        if (!element) {
            return false;
        }

        var currentValue = ' ' + this.get(element) + ' ';

        return currentValue.indexOf(' ' + value + ' ') >= 0;
    }
};
