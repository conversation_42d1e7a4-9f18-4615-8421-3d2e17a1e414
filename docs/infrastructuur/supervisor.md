# Supervisor

In productie gebruiken we Supervisor om lang-lopende processen te beheren.


## Dev-vm

Supervisor is ook besch<PERSON><PERSON><PERSON> in de dev-vm.

### Let op!

- Supervisor processen worden op dev-vm niet standaard gestart, dit moet je handmatig doen via CLI of Web UI
- Zorg dat je processen niet "per ongeluk" laat draaien. Dit kan veel invloed hebben op de beschikbare resources en schijfruimte van de ontwikkelomgeving

### Installatie

Alle Supervisor taken (programs) kunnen worden beheerd in `/.cicd/ansible/supervisord.yaml`.
In GitLab wordt er automatisch een actie aan de pipeline toegevoegd waarbij we de nieuwe configuratie in productie kunnen activeren.  

Om deze configuratie ook op je dev-vm te activeren dien je eerst de INI file bij te werken:
```shell
bin/console develop:supervisor:generate-config
```

Werk vervolgens je lokale supervisor bij:
```shell
bin/update-supervisor
```

Dit laatste command wordt ook automatisch uitgevoerd tijdens een `composer install`.

<PERSON><PERSON> commands kun je ook draaien met `composer update-supervisor`. 

## Web Interface

Zie de supervisor web api op je dev-vm:

- http://devX.office.vinden.nl:9001/
- supervisor / password

### Gebruik

- Je kan een process beheren met Start/Restart/Stop
- Process output kan je bekijken door op de program name te klikken
    - Als je meer regels tegelijk wilt zien kan je de `limit` query parameter verhogen. Tip; als je op de refresh knop klikt, wordt deze automatisch toegevoegd aan de url. Je kan hem dan makkelijker verhogen.

### Weetjes

- stdout/stderr werken niet goed, hier wordt geen output afgevangen. Die kan je beter niet gebruiken
- tail -f werkt niet goed via de web interface. Daar kan je beter CLI voor gebruiken

## CLI Interface
Je kan Supervisor ook via CLI beheren met `supervisorctl`.

### Status inzien
```shell
supervisorctl status
```

### Proces tailen
Gebruik hier de volledige program name, inclusief proces nummer toevoeging. Dit komt overeen met hoe het proces heet in het status overzicht. Je kan namelijk meerdere processen tegelijk draaien als dat voor een job geconfigureerd staat.

```shell
supervisorctl tail -f import-consumer:00
```

### Alle processen stoppen
```shell
supervisorctl stop all
```

### Meer info
```shell
supervisorctl help
```
