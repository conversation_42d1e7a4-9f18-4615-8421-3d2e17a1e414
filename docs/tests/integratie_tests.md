# Integratie tests

## Website config tests
Er zijn vele tests die zich bezig houden met de website config. Dat kan bij aanpassingen aan de config en website settings soms vrij veel werk zijn om bij te werken.
Om dit te voorkomen is er een algemene brand config bestand voor integratie tests in het leven geroepen.

### Food brand
Het [food.json](../../tests/Integration/Helper/_config/config-api/artemis/brand-config/food.json) JSON brand config bestand bevat de meeste scenario's betreft settings waarop de tests draaien.
De [BrandConfigTestHelper.php](../../tests/Integration/Helper/BrandConfigTestHelper.php) levert de mogelijkheden aan om deze config in een test te gebruiken. 
