# Componenten

Omdat we veel dezelfde pagina's her<PERSON><PERSON> met kleine a<PERSON><PERSON><PERSON>, hebben we het componenten systeem in het leven geroepen.
Componenten zijn configureerbare, herbruikbare blokken waarmee de frontend weergave kan worden opgebouwd.

## Functionele werking

* JSON template wordt ingeladen
* JSON structuur wordt gevalideerd en genormaliseerd
* Component structuur wordt opgebouwd
* API Requests worden opgebouwd en verzonden (Google CSA, Composite Search API)
* Frontend weergave wordt opgebouwd dmv elk component op de juiste plek te renderen

## Technische werking

Voor de technische werking, zie [Search view](../architectuur/search_view.md).

## JSON Templates

JSON templates zijn JSON bestanden waarin componenten geladen worden. Deze bestanden kunnen ingeladen worden uit het Brand,
uit het thema, of uit de default map.

Voor meer informatie over bestands structuur, zie [Frontend Thema's](frontend_themas.md).

Voor een voorbeeld JSON template, zie:
[web_search_desktop.json](../../resources/shared/templates_json/web_search/web_search_desktop.json)

JSON Templates bevatten de volgende onderdelen

* `description` - Optioneel veld om een beschrijving van de template in te plaatsen. Bijv van welke template het een kopie is met welke wijzigingen.
* `layout`
    * `template` - Verplichte twig template die gebruikt moet worden. Dit bepaalt de hele layout van de pagina. De layout type twig template extend van deze ingevulde twig template. Je hoeft dus alleen de blocks te defineren die de layout template ondersteunt.

* `options` - Opties die voor dit template type zijn gedefineerd
* `container` - Opties voor de container als component, zie `ContainerComponent` voor de mogelijkheden
* `components` - Component configuraties die gerendered moeten worden op de pagina

**Voorbeeld json template configuratie:**

```json
{
    "description": "Default dsr template",
    "layout": {
        "template": "@theme/layout_default_components.html.twig"
    },
    "options": {
        "keyword_highlight": "forced_false",
        "organic_keyword_highlight": "forced_false",
        "page_head_tags_type": "display_search_related"
    },
    "container": {
        "layout": "dsr"
    },
    "components": [
        {
            "type": "brand_logo",
            "link_to_home": true
        },
        {
            "type": "columns",
            "one": [
                {
                    "type": "image"
                }
            ]
        },
        {
            "type": "footer",
            "components": [
                {
                    "type": "footer_navigation",
                    "layout": "dsr"
                }
            ]
        }
    ]
}
```

Elke component configuratie heeft:

* `type` - Het type component, dit is verplicht en bepaalt welk component door de backend gebruikt gaat worden
* overige component-specifieke opties

### Template varianten

Het is mogelijk om met de url parameter `tv=variant-naam` een variant van een json template in te laden.

Voorbeeld: Wanneer de url parameter `tv=banaan` aanwezig is, dan wordt er bij het laden van bijv. `resources/shared/templates_json/web_search/web_search_landing_desktop.json` eerst gekeken of `resources/shared/templates_json/web_search/web_search_landing_desktop-banaan.json` bestaat. Zo ja, wordt die gebruikt, zo nee, dan wordt de oorspronkelijk aangevraagde template gebruikt.

Het formaat voor de bestandsnaam is `template_naam-variant_naam.json`.

## Componenten

Zie voor de architectuur van componenten: [Componenten Architectuur](../architectuur/componenten.md).

### Conditionele componenten

Sommige componenten renderen zelf niets, maar kunnen in plaats daarvan bepalen of een groep andere componenten wel of
niet gerenderd moet worden.

*Voorbeeld*

```json
{
    "type": "split_test_matches",
    "one_of_variants": [
        "abc"
    ],
    "yes": [
        {
            "type": "related_terms",
            "zone": "i",
            "amount": 8
        }
    ],
    "no": [
        {
            "type": "related_terms",
            "zone": "i",
            "amount": 12
        }
    ]
}
```

*Synchrone condities*

In veel gevallen zijn de parameters voor een conditie altijd beschikbaar. Denk bijv. aan `split_test_matches`;
we weten gedurende de hele request wat de traffic source van de gebruiker is. Dus die conditie kan altijd worden uitgevoerd.
Dus wanneer de view data request wordt opgebouwd, kan de conditie eerst worden uitgevoerd om te weten welke groep
componenten de view data request mogen instellen.

Zie weer het voorbeeld van related terms:

```json
{
    "type": "split_test_matches",
    "one_of_variants": [
        "abc"
    ],
    "yes": [
        {
            "type": "related_terms",
            "zone": "i",
            "amount": 8
        }
    ],
    "no": [
        {
            "type": "related_terms",
            "zone": "i",
            "amount": 12
        }
    ]
}
```

Synchrone condities maken gebruik van [AbstractCondition](../../bundles/brand-website-bundle/src/Component/Generic/AbstractCondition/AbstractConditionComponent.php)

*Asynchrone condities*

Maar sommige condities zijn afhankelijk van bijv. de Composite Search API response. Denk bijv. aan het `has_organic_results`
component.

Voorbeeld van een Composite Search API afhankelijk component:

```json
{
    "type": "has_organic_results",
    "yes": [
        {
            "type": "google_csa_bottom_unit",
            "amount": 4,
            "repeated": 4
        }
    ],
    "no": [
        {
            "type": "search_bar"
        }
    ]
},
```

In dit geval kan de conditie niet worden uitgevoerd op het moment dat de view data request wordt opgebouwd. Want de
conditie is afhankelijk van de response van die request. In dit geval worden beide component groepen aangesproken
tijdens het opbouwen van de view data request.

De component renderer heeft wel een [HasOrganicResultsRenderer::getActiveSegment()](../../bundles/brand-website-bundle/src/Component/Web/HasOrganicResults/HasOrganicResultsRenderer.php)
methode, maar deze wordt niet aangesproken tijdens het opbouwen van de request data.

Als er data toegevoegd moet worden nadat de CSAPI requests zijn uitgevoerd dan moet de renderer de `ComponentRendererWithSearchApiDependencyInterface` implementeren en dan kan de `handleSearchApiCompleted`-method worden gebruikt om data toe te voegen. Die method wordt aangeroepen nadat de geregistreerde CSAPI requests zijn uitgevoerd.

Bijvoorbeeld het bijhouden van het aantal gerenderde resultaten moet in de `handleSearchApiCompleted`-method worden gedaan. Zie bijv. [ContentPageResultsRenderer::handleSearchApiCompleted()](../../bundles/brand-website-bundle/src/Component/Content/ContentPageResults/ContentPageResultsRenderer.php).

Asynchrone condities maken gebruik van [AbstractSearchApiConditionComponent](../../bundles/brand-website-bundle/src/Component/Generic/AbstractSearchApiCondition/AbstractSearchApiConditionComponent.php)

### Bijzondere componenten

De meeste componenten renderen alleen html. Maar er zijn ook uitzonderingen.

## Template JSON-schema

Het template JSON-schema [view_template.schema.json](../../bundles/brand-website-bundle/src/Resources/config/schemas/view_template.schema.json) controleert of het JSON-template voldoet aan de eisen.
Dit schema zorgt ook voor automatisch aanvullen tijdens het maken of bewerken van templates. Mochten er niet ondersteunde opties worden gebruikt, dan zal dit tijdens een commit een `warning` geven.
De componenten in het schema worden automatisch gegenereerd op basis van de `OptionResolvers` van de componenten. Draai onderstaande commando om het schema bij te werken:

```shell
bin/console-[project] serp:develop:update-json-schema
```

Alle projecten hebben (op dit moment) dezelfde componenten dus het maakt niet uit voor welk project het commando wordt uitgevoerd. Er is ook een unit test `UpdateTemplateJsonSchemaConsoleTest` om te
controleren of alle componenten zijn opgenomen in het schema. Als een nieuw/gewijzigd component een wijziging vereist in het schema, zal de test falen en moet het commando gedraaid worden om het
schema bij te werken.
