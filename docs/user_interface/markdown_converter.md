# Markdown Converter

Er is een markdown naar HTML converter die gebruik maakt van de CommonMarkConverter van [League/CommonMark](https://commonmark.thephpleague.com/).

## Twig filter

De markdown converter is aanwezig als een Twig filter. Voorbeeld:

```html
<div class="content">
    {{ content|markdown_to_html }}
</div>
```

## Factory

Gezien we alle controle willen hebben over wat er wel en niet mogelijk is met de MarkdownConverter hebben we een eigen factory.
Dit is de `Visymo\BrandWebsiteBundle\Markdown\MarkdownConverterFactory` welke een instantie van de `League\CommonMark\ConverterInterface` teruggeeft.

In deze factory bouwen we zelf de [League\CommonMark\Environment\Environment](https://commonmark.thephpleague.com/2.3/customization/environment/).
Dit heeft als voordeel dat we de volledige controle hebben over de extensies die we inladen.
Uiteindelijk geeft dit de concrete implementatie van de `ConverterInterface` terug in de vorm van de `League\CommonMark\MarkdownConverter`.

De configuratie voor de `Environment` wordt als constructor parameter aan de factory meegegeven en is configurabel via de `services.yaml`.

## Geactiveerde Extensies

### CommonMark extensies

#### DefaultAttributesExtension
- Deze maakt het mogelijk html-attributes in te stellen voor specifieke elementen.
- Configuratie hiervoor gaat via de [config](#config) array. 
- Zie ook: [DefaultAttributesExtension](https://commonmark.thephpleague.com/2.3/extensions/default-attributes/)

### Eigen extensies
We hebben een aantal eigen extensies, welke vaak zorgen dat bepaalde functionaliteit van markdown wordt ondersteund.

#### ListExtension
- Zorgt voor het ondersteunen van lists
- De ondersteuning voor markers is op te geven via de config, zie [Config](#config)

#### NewlineExtension
- Zorgt voor het ondersteunen van `\n`

#### VisymoExtension
- Voegt ondersteuning toe voor de `commonmark` configuratie in de config-array, dit in verband met dat gebruikte `Renderers` in eigen extensies uitgaan van de aanwezigheid hiervan.
- Voegt ondersteuning toe voor de basis `Renderers` van Markdown, zonder deze `Renderers` functioneert de `MarkdownConverter` niet
  - `DocumentRenderer`
  - `ParagraphRenderer`
  - `TextRenderer`

## Config

De CommonMarkConverter is voorzien van een config om oa de paragrafen en lijsten van CSS classes te voorzien. Zie [services.yaml](../../bundles/brand-website-bundle/src/Resources/config/services/services.yaml).
```yaml
Visymo\BrandWebsiteBundle\Markdown\MarkdownConverterFactory:
    arguments:
        $config:
            html_input: 'escape'
            allow_unsafe_links: false
            commonmark:
                enable_em: false
                enable_strong: false
                use_asterisk: false
                use_underscore: false
                unordered_list_markers: [ '*' ]
            default_attributes:
                League\CommonMark\Extension\CommonMark\Node\Block\Heading:
                    class: [ 'text-heading' ]
                League\CommonMark\Node\Block\Paragraph:
                    class: [ 'text-paragraph' ]
                League\CommonMark\Extension\CommonMark\Node\Block\ListBlock:
                    class: [ 'text-list' ]
                League\CommonMark\Extension\CommonMark\Node\Block\ListItem:
                    class: [ 'text-list__item' ]
```

## Styling

De HTML die uit de markdown converter komt rollen krijgt standaard geen styling mee. Voeg deze aan de component styling toe. Zie als voorbeeld [contentPageParagraphDefault.scss](../../bundles/brand-website-bundle/src/Component/Content/ContentPageParagraph/layout/default/contentPageParagraphDefault.scss).  
