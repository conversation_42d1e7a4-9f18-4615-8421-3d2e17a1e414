# Full Stack Conference

Er zijn veel talks geweest, ook een workshop waarbij dieper werd ingegaan op de structuren en werkingen van teams.
Per talk en workshop hieronder een korte samenvatting en wat we hier mogelijk uit kunnen halen.

## Workshop Team Topology

In deze workshop zijn we in teams bezig geweest om verschillende real life voorbeelden te analyseren en daarbij te kijken op basis van aangegeven teamstructuren, team typen en manieren van samenwerkingen.

Zie ook de bijgesloten presentatie hierover voor enkele voorbeelden van de teamstructuren, etc.

Fast Flow
[https://www.youtube.com/watch?v=nkP6oOGuN2g](https://www.youtube.com/watch?v=nkP6oOGuN2g)

## Keynotes

### Integrating AI into your app

Dit was een interessante talk voor als we ooit zelf een AI model willen maken, maar voor de korte termijn bood het weinig interessante informatie.

Tijdens de talk werd duidelijk dat het heel belangrijk is om de juiste type model te selecteren, deze kunnen worden opgezocht via [https://huggingface.co/](https://huggingface.co/), een open source community waarin het mogelijk en makkelijk is om alle verschillende models op te zoeken.
Belangrijk ook is om rekening te houden met de max input token length. Als dit wordt overschreden bij een agent request kan dit zorgen voor onjuiste reacties als hallucinaties, etc.

Voor het trainen van een model werd Google Colab gebruikt, een python library die veel mogelijkheden biedt. Hoe het werkt, afgezien van een aantal parameters, werd niet heel diep op ingegaan.

### Reaching operational excellence

[https://nl.wikisage.org/wiki/Operational_excellence](https://nl.wikisage.org/wiki/Operational_excellence)

Dit was een wat meer interactieve keynote waarbij de focus OpenTelemetry was. Hierbij waren twee demo's getoond: Tracey Bird (Flappy Bird clone) en een soort van community played guitar hero.

#### Tracey bird

We mochten een QR code scannen en het spel spelen. Precies zoals bij Flappy Bird kon je tikken om je player character naar boven te laten bewegen en ging je af als je een muur raakte.
Na enkele pogingen te hebben gehad werd er geschakeld naar een diagram van de infrastructuur achter de app. Dit betrof een Unity app geschreven met Javascript, 2 Kotlin backends, een Collector en Grafana.
De Collector was uiteindelijk verantwoordelijk voor het filteren van data (AVG gevoelige data werd eruit gefiltered bijvoorbeeld) en diende als een single point of entry vanuit andere netwerken, ook voor Grafana.

In Grafana werd de data die door OpenTelemetry verzameld werd getoond, bij welke muur gingen spelers af, hoe vaak werd er getikt, high score, etc.
Per event konden we data inzien en werd er een trace getoond zoals wij die kennen van Exceptions. Per API call naar de backend was een er waterfall view zoals in de Chrome devtools network tab getoond wordt en werden al observaties gedaan waarin verbeteringen kunnen worden gemaakt om de app meer responsive te maken.

#### Guitar hero

Dit was een heel simpele app voor de gebruiker, op de beamer werd de "bladmuziek" aan emoji's getoond (gitaar, drums, etc.) en wij als publiek moesten op onze devices gezamenlijk de juiste combinaties doorgeven. Dit lijkte erg op de Twitch plays pokemon [https://en.wikipedia.org/wiki/Twitch_Plays_Pok%C3%A9mon](https://en.wikipedia.org/wiki/Twitch_Plays_Pok%C3%A9mon) en hierna werden zoals bij Tracey Bird statistieken getoond.

### Property based testing

Vaak word er gebruik gemaakt van example based testing:

```php
$result = Calculator::add(1, 4);
$this->assertEquals(5, $result);
```

Hoewel dit in theorie correct is laat het lijken alsof de waarden iets betekenen in de scope van de applicatie. De nummers zouden uiteindelijk niet uit mogen maken, hiervoor was in de Pascal wereld QuickCheck opgezet [https://en.wikipedia.org/wiki/QuickCheck](https://en.wikipedia.org/wiki/QuickCheck).
QuickCheck bestaat uit twee delen: Generators en Shrinking.

Een generator generate random waarden binnen de scope van die generator, dus bijvoorbeeld een IntegerGenerator, StringGenerator, ColorGenerator, DateGenerator, etc.
Shrinking komt van pas op het moment dat er een fout plaatsvindt in een test. Voorbeeld werd gegeven met een IntegerGenerator. In de test werd een `for($i = 0; $i <= 100; $i++)` gebruikt, bij 81 en hoger ging het fout. 81 is dan de kleinste waarde waarbij het fout gaat. Uit de test output krijg je dan de code die je nodig hebt om die test te herproduceren zonder zlef wat te moeten hardcoden.

PHP libraries voor QuickCheck:
- [https://github.com/giorgiosironi/eris](https://github.com/giorgiosironi/eris)
- [https://github.com/steos/php-quickcheck](https://github.com/steos/php-quickcheck)

Mijn conclusie hier is dat we al een vorm van property based testing gebruiken met de TestStubRandomizer en is vooral de benaming nieuw, maar wellicht kunnen we hier wel nog wat uit leren.

### Trusting AI to modernize software at scale

Het bedrijf Moderne heeft een AI based tool gemaakt die het mogelijk maakt om je alle repositories in een organisatie te laten bijwerken op bijvoorbeeld Java versie, maar ook dependencies.
Dit loopt dan de code door en kijkt welke changes nodig zijn om een versie te kunnen upgraden en stelt dan die changes (of meerdere voorstellen) voor voordat deze worden doorgevoerd.

[https://www.moderne.ai/](https://www.moderne.ai/)

Hoewel het indrukwekkend was hoe het platform werkt, hebben we al Renovate die een aantal taken automatiseerd en leek het verder een vrij dure tool voor wat het verder doet.

### Lighting talks

#### Hidden costs of "ready made" platforms

Deze talk ging over een platform die experimenteel werd toegepast in een hackathon, maar vervolgens ook in productie kwam intern. Het lostte geen problemen op, zorgde er enkel voor dat pijnpunten, config bestanden die leefden op de harde schijf van elke gebruiken, in private cloud buckets werden geplaatst.
Lang verhaal kort, ze moesten intern overleggen wat de wensen zijn, wat mensen irritant vinden en daar een oplossing voor bedenken ipv er maar een cloudoplossing neer te zetten.

#### Accessibility on web

Voor ons een bekende talk, weinig meer over te vertellen. Wel werd nog een mooie tool getoond, namelijk [https://www.a11yproject.com/checklist/](https://www.a11yproject.com/checklist/). Hiermee hoef je enkel de checklist naast een applicatie te leggen om een aantal basis criteria na te lopen.

### Seven habits of a highly succesful team

Seven habits die volgens AI belangrijk zijn, die de spreker onderbouwde met eigen ervaringen. Ook veel opmerkingen/negativiteit over scrum.
Hoop dat de talk nog een keer online als video komt, leuk om eens te kijken, maar weinig om er zelf uit te halen.





























