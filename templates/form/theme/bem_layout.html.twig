{% use "form_div_layout.html.twig" %}

{# Widgets #}
{%- block widget_attributes -%}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__control')|trim}) %}
    {%- if not valid %}
        {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__control--invalid')|trim}) %}
    {% endif -%}
    {{ parent() }}
{%- endblock widget_attributes -%}

{%- block textarea_widget -%}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__control--textarea')|trim}) %}
    {{ parent() }}
{%- endblock textarea_widget -%}

{%- block adsense_style_id_widget -%}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__control--adsense-style-id')|trim}) %}
    {{ block('form_widget_simple') }}
{%- endblock adsense_style_id_widget -%}

{%- block choice_widget_expanded -%}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__choices')|trim}) %}
    <div {{ block('widget_container_attributes') }}>
        {%- for child in form %}
            {{- form_widget(child) -}}
        {% endfor -%}
    </div>
{%- endblock choice_widget_expanded -%}

{%- block choice_widget_collapsed -%}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__control--select')|trim}) %}
    {{ parent() }}
{%- endblock choice_widget_collapsed -%}

{%- block checkbox_widget -%}
    {% set attr = attr|merge({type: 'checkbox'}) %}
    {{ block('choice_type_widget') }}
{%- endblock checkbox_widget -%}

{%- block radio_widget -%}
    {% set attr = attr|merge({type: 'radio'}) %}
    {{ block('choice_type_widget') }}
{%- endblock radio_widget -%}

{%- block choice_type_widget -%}
    {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__choice-control form__choice-control--' ~ attr.type)|trim}) %}
    <div class="form__choice form__choice--{{ attr.type }}{{ attr.disabled is defined ? ' form__choice--disabled' }}">
        <label class="form__choice-label">
            <input type="{{ attr.type }}" {{ block('widget_attributes') }}{% if value is defined %} value="{{ value }}"{% endif %}{% if checked %} checked{% endif %} />
            {% if label is not empty %}
                <span class="form__choice-text">{{ label }}</span>
            {% endif %}
        </label>
    </div>
{%- endblock choice_type_widget %}

{% block list_widget %}
    <div class="form__list">
        {% if form.vars.value.message is defined %}
            <div class="form__list-message">{{ form.vars.value.message }}</div>
        {% endif %}
        <ul class="form__list-items">
            {% if form.vars.value.items is iterable %}
                {% for item in form.vars.value.items %}
                    <li class="form__list-item">{{ item }}</li>
                {% endfor %}
            {% else %}
                <li class="form__list-item">No items to display</li>
            {% endif %}
        </ul>
    </div>
{% endblock %}

{% block datalist_widget %}
    <div class="form__datalist">
        {% set attr = attr|merge({class: (attr.class|default('') ~ ' form__control')|trim}) %}
        {% set uniqueId = 'form__datalist-' ~ form.vars.name %}
        <input
            id="{{ form.vars.name }}"
            name="{{ form.vars.full_name }}"
            class="{{ attr.class }}"
            list="{{ uniqueId }}"
            placeholder="{{ form.vars.placeholder|default('') }}"
            value="{{ value }}"
            {{ block('widget_attributes') }}
        />

        <datalist id="{{ uniqueId }}">
            {% if form.vars.items is iterable %}
                {% for item in form.vars.items %}
                    <option value="{{ item }}"></option>
                {% endfor %}
            {% endif %}
        </datalist>
    </div>
{% endblock %}

{%- block button_widget -%}
    {%- set attr = attr|merge({class: (attr.class|default('button--secondary') ~ ' button')|trim}) -%}
    {{ parent() }}
{%- endblock button_widget %}

{% block submit_widget %}
    <twig:Button submit="1" disabled="{{ disabled ? 1 : 0 }}">{{ field_label(form) }}</twig:Button>
{% endblock %}

{# Labels #}
{%- block form_label -%}
    {% if label is not same as(false) -%}
        {% set label_attr = label_attr|merge({'class': (label_attr.class|default('') ~ ' form__label')|trim}) %}
        {% if not compound -%}
            {% set label_attr = label_attr|merge({'for': id}) %}
        {%- endif -%}
        {% if required -%}
            {% set label_attr = label_attr|merge({'class': (label_attr.class|default('') ~ ' form__label--required')|trim}) %}
        {%- endif -%}
        {% if label is empty -%}
            {%- if label_format is not empty -%}
                {% set label = label_format|replace({
                    '%name%': name,
                    '%id%': id,
                }) %}
            {%- else -%}
                {% set label = name|humanize %}
            {%- endif -%}
        {%- endif -%}
        <{{ element|default('label') }}{% if label_attr %}{% with { attr: label_attr } %}{{ block('attributes') }}{% endwith %}{% endif %}>
        {%- if label_html is same as(false) -%}
            {{- label -}}
        {%- else -%}
            {{- label|raw -}}
        {%- endif -%}
        </{{ element|default('label') }}>
    {%- endif -%}
{%- endblock form_label %}

{# Rows #}
{%- block form_row -%}
    <div class="form__row">
        {{- form_label(form) -}}
        {{- form_help(form) -}}
        {{- form_widget(form) -}}
        {{- form_errors(form) -}}
    </div>
{%- endblock form_row %}

{# Form #}
{%- block form_start -%}
    {%- set attr = attr|merge({class: (attr.class|default('') ~ ' form')|trim}) -%}
    {{ parent() }}
{%- endblock form_start -%}

{# Misc #}
{%- block form_errors -%}
    {%- if errors|length > 0 -%}
        <ul class="form__errors">
            {%- for error in errors -%}
                <li class="form__error">{{ error.message|nl2br }}</li>
            {%- endfor -%}
        </ul>
    {%- endif -%}
{%- endblock form_errors -%}
