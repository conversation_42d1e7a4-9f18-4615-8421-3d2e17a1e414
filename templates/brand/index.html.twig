{# @var brands Domain\Brand\Brand[] #}
{# @var data_filter_matcher Infrastructure\Service\DataFilter\Matcher\DataFilterMatcherInterface #}
{% extends 'base.html.twig' %}

{% block body %}
    <twig:Card>
        <twig:ButtonLink url="{{ path('route_brand_create') }}">Create brand</twig:ButtonLink>
        <twig:ButtonLink url="{{ path('route_brand_config_push_all') }}" confirm>Push all configs</twig:ButtonLink>
        <twig:TableToggle/>
    </twig:Card>
    <twig:Card modifiers="content-vertical">
        <twig:DataFilter dataFilterMatcher="{{ data_filter_matcher }}"/>
    </twig:Card>
    <twig:Card>
        <twig:Table>
            <twig:TableHead modifiers="sticky">
                <tr>
                    <th>
                        <twig:TableSortLink sort="slug">Brand</twig:TableSortLink>
                    </th>
                    <th>
                        <twig:TableSortLink sort="active">Active</twig:TableSortLink>
                    </th>
                    <th>
                        <twig:TableSortLink sort="partner_slug">Partner</twig:TableSortLink>
                    </th>
                    <th>
                        <twig:TableSortLink sort="adsense_contract_type" title="AdSense contract type">Contract</twig:TableSortLink>
                    </th>
                    <th>
                        <twig:TableSortLink sort="status">Status</twig:TableSortLink>
                    </th>
                    <th class="table__cell table__cell--no-wrap">
                        <twig:TableSortLink sort="last_imported_at">Last import</twig:TableSortLink>
                    </th>
                    <th class="table__cell table__cell--no-wrap">
                        <twig:TableSortLink sort="last_pushed_at" title="Last time a valid config was pushed to brand websites">Last push</twig:TableSortLink>
                    </th>
                    {% if not is_toggle_active() %}
                        <th class="table__cell">
                            <twig:TableSortLink sort="google_tag_manager" title="Google Tag Manager">GTM</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="google_tag_manager_id" title="Google Tag Manager ID">GTM ID</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="google_publisher_tag" title="Google Publisher Tag">GPT</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="google_publisher_tag_ad_unit_path" title="Google Publisher Tag Ad unit path">GPT Ad unit path</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="spam_click_detect" title="Spam Click Detect">SCD</twig:TableSortLink>
                        </th>
                    {% endif %}
                    <th>
                        <twig:TableSortLink sort="web_search" title="Web Search">WS</twig:TableSortLink>
                    </th>
                    <th>
                        <twig:TableSortLink sort="display_search_related" title="Display Search Related">DSR</twig:TableSortLink>
                    </th>
                    <th>
                        <twig:TableSortLink sort="search">Search</twig:TableSortLink>
                    </th>
                    {% if not is_toggle_active() %}
                        <th class="table__cell">
                            <twig:TableSortLink sort="search_seo">SEO</twig:TableSortLink>
                        </th>
                    {% endif %}
                    <th>
                        <twig:TableSortLink sort="display_search" title="Display Search">DS</twig:TableSortLink>
                    </th>
                    <th>
                        <twig:TableSortLink sort="microsoft_search_related" title="Microsoft Search Related">MSR</twig:TableSortLink>
                    </th>
                    {% if not is_toggle_active() %}
                        <th class="table__cell">
                            <twig:TableSortLink sort="pageview_conversion" title="Pageview conversion">PC</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="campaign_name_validation" title="Campaign name validation">CNV</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="article" title="Article page enabled">Art</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="content_page_collection" title="Content page collection">Collection</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="content_page_home_type" title="Content page home type">CPH</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="organic_result_route" title="Organic result route">ORR</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="json_template_variant" title="JSON template variant">TV</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="json_template_overrides" title="JSON template overrides">JTO</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="cheq">Cheq</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="bing_ads_approval" title="Bing Ads approval">BA</twig:TableSortLink>
                        </th>
                        <th class="table__cell">
                            <twig:TableSortLink sort="microsoft_search" title="Microsoft Search">MS</twig:TableSortLink>
                        </th>
                    {% endif %}
                    <th></th>
                </tr>
            </twig:TableHead>
            <tbody>
                {% for brand in brands %}
                    <tr
                        class="table__row"
                        data-slug="{{ brand.slug }}"
                        data-partner="{{ brand.partnerSlug }}"
                    >
                        <td>
                            <twig:TableLink url="{{ path('route_brand_edit', {slug: brand.slug}) }}" title="Edit brand">{{ brand.slug }}</twig:TableLink>
                        </td>
                        <td>
                            {% if brand.active %}
                                <i class="bi bi-check-circle table__info--success" title="Brand is active"></i>
                            {% else %}
                                <i class="bi bi-exclamation-circle table__info--warning" title="Brand is inactive"></i>
                            {% endif %}
                        </td>
                        <td>{{ brand.partnerSlug|default('-') }}</td>
                        <td>{{ brand.adSenseContractType.value|default('-') }}</td>
                        <td>
                            {%- if brand.status.isMissing() -%}
                                <span class="table__info--warning">
                                    {{- brand.status.label -}}
                                </span>
                            {%- elseif not brand.status.isSuccess() -%}
                                <twig:TableLink url="{{ path('route_api_brand_config_brand_invalid', {slug: brand.slug}) }}" modifier="warning" target="_blank">
                                    {{- brand.status.label -}}
                                </twig:TableLink>
                            {%- else -%}
                                {{ brand.status.label }}
                            {%- endif -%}
                        </td>
                        <td class="table__cell table__cell--no-wrap">{{ brand.lastImportedAt|date }}</td>
                        <td class="table__cell table__cell--no-wrap">{{ brand.lastPushedAt is not null ? brand.lastPushedAt|date : '-' }}</td>
                        {% if not is_toggle_active() %}
                            <td class="table__cell">{{ brand.googleTagManager.enabled|default(false) ? 'Yes' : '-' }}</td>
                            <td class="table__cell">{{ brand.googleTagManager.googleTagManagerId|default('-') }}</td>
                            <td class="table__cell">{{ brand.googlePublisherTag.enabled|default(false) ? 'Yes' : '-' }}</td>
                            <td class="table__cell">{{ brand.googlePublisherTag.adUnitPath|default('-') }}</td>
                            <td class="table__cell">{{ brand.spamClickDetect.enabled|default(false) ? 'Yes' : '-' }}</td>
                        {% endif %}
                        <td>{{ brand.webSearch.enabled|default(false) ? 'Yes' : '-' }}</td>
                        <td>{{ brand.displaySearchRelated.enabled|default(false) ? 'Yes' : '-' }}</td>
                        <td>{{ brand.search.enabled|default(false) ? 'Yes' : '-' }}</td>
                        {% if not is_toggle_active() %}
                            <td class="table__cell">{{ brand.search.seoEnabled|default(false) ? 'Yes' : '-' }}</td>
                        {% endif %}
                        <td>{{ brand.displaySearch.enabled|default(false) ? 'Yes' : '-' }}</td>
                        <td>{{ brand.microsoftSearchRelated.enabled|default(false) ? 'Yes' : '-' }}</td>
                        {% if not is_toggle_active() %}
                            <td class="table__cell">{{ brand.pageviewConversion.enabled|default(false) ? 'Yes' : '-' }}</td>
                            <td class="table__cell">{{ brand.tracking.campaignNameValidationEnabled|default(false) ? 'Yes' : '-' }}</td>
                            <td class="table__cell">{{ brand.article.enabled|default(false) ? 'Yes' : '-' }}</td>
                            <td class="table__cell">{{ brand.contentPage.collection|default('-') }}</td>
                            <td class="table__cell">{{ brand.contentPageHome.type.value|default('-') }}</td>
                            <td class="table__cell">{{ brand.contentPage.organicResultRoute.path()|default('-') }}</td>
                            <td class="table__cell">{{ brand.jsonTemplate.templateVariant|default('-') }}</td>
                            <td class="table__cell">{{ brand.jsonTemplate.templateOverrides|default('-') }}</td>
                            <td class="table__cell">{{ brand.cheq.enabled|default(false) ? 'Yes' : '-' }}</td>
                            <td class="table__cell">{{ brand.hasBingAdsApproval ? 'Yes' : '-' }}</td>
                            <td class="table__cell">{{ brand.microsoftSearch.enabled|default(false) ? 'Yes' : '-' }}</td>
                        {% endif %}
                        <td class="table__cell table__cell--no-wrap">
                            {%- if not brand.status.isMissing() -%}
                                <twig:ButtonLink url="{{ path('route_api_brand_config_brand', {slug: brand.slug}) }}" title="View brand config" modifiers="small" target="_blank">
                                    <i class="bi bi-cpu-fill"></i>
                                </twig:ButtonLink>
                            {%- endif -%}
                            <twig:ButtonLink url="{{ path('route_brand_audit_log', {slug: brand.slug}) }}" title="Audit log" modifiers="small">
                                <i class="bi bi-file-text"></i>
                            </twig:ButtonLink>
                            <twig:ButtonLink url="{{ path('route_brand_assets', {slug: brand.slug}) }}" title="Brand assets" modifiers="small">
                                <i class="bi bi-image-fill"></i>
                            </twig:ButtonLink>
                            {%- if not brand.status.isMissing() -%}
                                <twig:ButtonLink url="{{ path('route_brand_config_push_brand', {slug: brand.slug}) }}" title="Push brand config" confirm modifiers="small">
                                    <i class="bi bi-rocket-takeoff-fill"></i>
                                </twig:ButtonLink>
                                {% if not brand.hasGoogleAdSenseApproval %}
                                    <twig:ButtonLink url="{{ path('route_brand_google_adsense_approval', {slug: brand.slug}) }}" target="_blank" confirm title="Google AdSense Approval" modifiers="small">
                                        <i class="bi bi-google"></i>
                                    </twig:ButtonLink>
                                {% endif %}
                            {% endif %}
{#                            {%- if brand.status.isMissing() -%}#}
{#                                <twig:ButtonLink url="{{ path('route_brand_edit_slug', {slug: brand.slug}) }}" title="Edit brand slug" modifiers="small">#}
{#                                    <i class="bi bi-pencil-square"></i>#}
{#                                </twig:ButtonLink>#}
{#                            {%- endif -%}#}
                            <twig:ButtonLink url="{{ get_kibana_access_log_url_for_slug(brand.slug) }}" title="View access log" modifiers="small" target="_blank">
                                <i class="bi bi-bar-chart-fill"></i>
                            </twig:ButtonLink>
                            <twig:ButtonLink url="{{ get_kibana_conversion_url_for_slug(brand.slug) }}" title="View conversion log" modifiers="small" target="_blank">
                                <i class="bi bi-graph-up"></i>
                            </twig:ButtonLink>
                            <twig:ButtonLink url="{{ path('route_styles_vbb_index', {expression: brand.slug}) }}" title="View VBB styles" modifiers="small">
                                <i class="bi bi-collection-play-fill"></i>
                            </twig:ButtonLink>
                        </td>
                    </tr>
                {% else %}
                    <tr class="table__row">
                        <td colspan="100%">No brands found</td>
                    </tr>
                {% endfor %}
            </tbody>
        </twig:Table>
    </twig:Card>
{% endblock %}
