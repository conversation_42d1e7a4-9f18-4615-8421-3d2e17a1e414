<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Request\Pagination;

use Infrastructure\Service\GitLabApi\Request\GitLabApiSortRequest;

final class PaginationRequestFactory
{
    public function create(
        ?int $page = null,
        ?int $maxPage = null,
        ?int $perPage = null,
        ?string $orderBy = null,
        ?GitLabApiSortRequest $sort = null
    ): PaginationRequest
    {
        $collectAllRows = $page === null;
        $page = max((int)$page, 1);

        return new PaginationRequest(
            page          : $page,
            maxPage       : $maxPage,
            collectAllRows: $collectAllRows,
            perPage       : $perPage,
            orderBy       : $orderBy,
            sort          : $sort
        );
    }
}
