<?php

declare(strict_types=1);

namespace Infrastructure\Service\GitLabApi\Resource\Pipeline\GetPipelines;

use Domain\GitLabApi\Pipeline\PipelineScope;
use Domain\GitLabApi\Pipeline\PipelineSource;
use Domain\GitLabApi\Pipeline\PipelineStatus;
use Infrastructure\Service\GitLabApi\Request\GitLabApiPaginationRequestInterface;
use Infrastructure\Service\GitLabApi\Request\GitLabApiProjectRequestInterface;
use Infrastructure\Service\GitLabApi\Request\Method\HttpRequestMethod;
use Infrastructure\Service\GitLabApi\Request\Pagination\PaginationRequestInterface;

final class GetPipelinesRequest implements GitLabApiProjectRequestInterface, GitLabApiPaginationRequestInterface
{
    public function __construct(
        private readonly int $projectId,
        private readonly ?string $name = null,
        private readonly ?string $ref = null,
        private readonly ?string $sha = null,
        private readonly ?PipelineStatus $status = null,
        private readonly ?PipelineScope $scope = null,
        private readonly ?PipelineSource $source = null,
        private readonly ?PaginationRequestInterface $paginationRequest = null
    )
    {
    }

    public function getProjectId(): int
    {
        return $this->projectId;
    }

    public function getMethod(): HttpRequestMethod
    {
        return HttpRequestMethod::GET;
    }

    public function getEndpoint(): string
    {
        return 'pipelines';
    }

    public function getPagination(): ?PaginationRequestInterface
    {
        return $this->paginationRequest;
    }

    /**
     * @inheritDoc
     */
    public function getParameters(): array
    {
        return [
            'name'   => $this->name,
            'ref'    => $this->ref,
            'sha'    => $this->sha,
            'status' => $this->status?->value,
            'scope'  => $this->scope?->value,
            'source' => $this->source?->value,
        ];
    }

    /**
     * @inheritDoc
     */
    public function getPayload(): ?array
    {
        return null;
    }
}
