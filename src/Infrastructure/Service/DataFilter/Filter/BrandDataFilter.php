<?php

declare(strict_types=1);

namespace Infrastructure\Service\DataFilter\Filter;

use Domain\Brand\Brand;
use Domain\Brand\ContractType\AdSenseContractType;
use Infrastructure\Service\DataFilter\Collection\DataFilterCollection;
use Infrastructure\Service\DataFilter\ExpressionNode\BooleanExpressionNode;
use Infrastructure\Service\DataFilter\ExpressionNode\DateExpressionNode;
use Infrastructure\Service\DataFilter\ExpressionNode\StringExpressionNode;
use Visymo\Shared\Domain\DateTime\DateTimeFormat;

final readonly class BrandDataFilter implements DataFilterInterface
{
    private const string NAME_ACTIVE                   = 'active';
    private const string NAME_ARTICLE                  = 'article';
    private const string NAME_COLLECTION               = 'collection';
    private const string NAME_DIRECT                   = 'direct';
    private const string NAME_DISPLAY_SEARCH           = 'ds';
    private const string NAME_DISPLAY_SEARCH_RELATED   = 'dsr';
    private const string NAME_LAST_IMPORT              = 'last_import';
    private const string NAME_LAST_PUSHED_AT           = 'last_push';
    private const string NAME_MICROSOFT_SEARCH_RELATED = 'msr';
    private const string NAME_ONLINE                   = 'online';
    private const string NAME_ORGANIC                  = 'organic';
    private const string NAME_ORGANIC_RESULT_ROUTE     = 'orr';
    private const string NAME_PARTNER                  = 'partner';
    private const string NAME_SLUG                     = 'slug';
    private const string NAME_TEMPLATE_VARIANT         = 'tv';
    private const string NAME_JSON_TEMPLATE_OVERRIDES  = 'jto';
    private const string NAME_WEB_SEARCH               = 'ws';

    public function getCollection(): DataFilterCollection
    {
        return DataFilterCollection::BRAND;
    }

    /**
     * @inheritDoc
     */
    public function getExpressionNodes(): array
    {
        return [
            BooleanExpressionNode::create(self::NAME_ACTIVE, 'Active'),
            BooleanExpressionNode::create(self::NAME_ARTICLE, 'Article'),
            StringExpressionNode::create(self::NAME_COLLECTION, 'Content collection'),
            StringExpressionNode::create(self::NAME_DIRECT, 'Direct AdSense contract type'),
            BooleanExpressionNode::create(self::NAME_DISPLAY_SEARCH, 'Display Search'),
            BooleanExpressionNode::create(self::NAME_DISPLAY_SEARCH_RELATED, 'Display Search Related'),
            StringExpressionNode::create(self::NAME_JSON_TEMPLATE_OVERRIDES, 'JSON template overrides'),
            DateExpressionNode::create(self::NAME_LAST_IMPORT, 'Last import'),
            DateExpressionNode::create(self::NAME_LAST_PUSHED_AT, 'Last push'),
            BooleanExpressionNode::create(self::NAME_MICROSOFT_SEARCH_RELATED, 'Microsoft Search Related'),
            StringExpressionNode::create(self::NAME_ONLINE, 'Online AdSense contract type'),
            BooleanExpressionNode::create(self::NAME_ORGANIC, 'As organic result'),
            StringExpressionNode::create(self::NAME_ORGANIC_RESULT_ROUTE, 'Organic result route'),
            StringExpressionNode::create(self::NAME_PARTNER, 'Partner slug'),
            StringExpressionNode::create(self::NAME_SLUG, 'Brand slug'),
            StringExpressionNode::create(self::NAME_TEMPLATE_VARIANT, 'JSON template variant'),
            BooleanExpressionNode::create(self::NAME_WEB_SEARCH, 'Web Search'),
        ];
    }

    /**
     * @inheritDoc
     */
    public function getItemValues(mixed $item): array
    {
        if (!$item instanceof Brand) {
            return [];
        }

        $isOrganic = $item->contentPage !== null &&
                     $item->contentPage->enabled &&
                     $item->contentPage->useBrandForOrganicResults;

        return [
            self::NAME_ACTIVE                   => $item->active,
            self::NAME_ARTICLE                  => $item->article?->enabled,
            self::NAME_COLLECTION               => $item->contentPage?->collection,
            self::NAME_DIRECT                   => $item->adSenseContractType === AdSenseContractType::DIRECT,
            self::NAME_DISPLAY_SEARCH           => $item->displaySearch?->enabled,
            self::NAME_DISPLAY_SEARCH_RELATED   => $item->displaySearchRelated?->enabled,
            self::NAME_JSON_TEMPLATE_OVERRIDES  => $item->jsonTemplate?->templateOverrides,
            self::NAME_LAST_IMPORT              => $item->lastImportedAt->format(DateTimeFormat::DATE_TIME),
            self::NAME_LAST_PUSHED_AT           => $item->lastPushedAt?->format(DateTimeFormat::DATE_TIME),
            self::NAME_MICROSOFT_SEARCH_RELATED => $item->microsoftSearchRelated?->enabled,
            self::NAME_ONLINE                   => $item->adSenseContractType === AdSenseContractType::ONLINE,
            self::NAME_ORGANIC                  => $isOrganic,
            self::NAME_ORGANIC_RESULT_ROUTE     => $item->contentPage?->organicResultRoute?->path(),
            self::NAME_PARTNER                  => $item->partnerSlug,
            self::NAME_SLUG                     => $item->slug,
            self::NAME_TEMPLATE_VARIANT         => $item->jsonTemplate?->templateVariant,
            self::NAME_WEB_SEARCH               => $item->webSearch?->enabled,
        ];
    }

    public function canHandleExpressionAsQuery(string $expression): bool
    {
        $pattern = sprintf('/%s/', Brand::SLUG_REGEX);

        return preg_match($pattern, $expression) === 1;
    }

    public function doesItemMatchesQuery(mixed $item, string $query): bool
    {
        if (!$item instanceof Brand) {
            return false;
        }

        return str_contains($item->slug, $query);
    }
}
