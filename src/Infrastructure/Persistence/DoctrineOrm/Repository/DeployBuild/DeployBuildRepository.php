<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\DeployBuild;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\DeployBuild\DeployBuild;
use Domain\DeployBuild\DeployBuildRepositoryInterface;

final readonly class DeployBuildRepository implements DeployBuildRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    public function findOneByDeployProjectIdAndBuild(int $deployProjectId, string $build): ?DeployBuild
    {
        return $this->getRepository()->findOneBy(
            [
                'project' => $deployProjectId,
                'build'   => $build,
            ]
        );
    }

    public function store(DeployBuild $deployBuild): void
    {
        $this->entityManager->persist($deployBuild);
        $this->entityManager->flush();
    }

    public function delete(DeployBuild $deployBuild): void
    {
        $this->entityManager->remove($deployBuild);
        $this->entityManager->flush();
    }

    /**
     * @return EntityRepository<DeployBuild>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(DeployBuild::class);
    }
}
