<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\UrlScan;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\UrlScanContent\Exception\UrlScanContentNotFoundException;
use Domain\UrlScanContent\UrlScanContent;
use Domain\UrlScanContent\UrlScanContentRepositoryInterface;

final readonly class UrlScanContentRepository implements UrlScanContentRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    public function findOneById(int $id): UrlScanContent
    {
        $urlScanContent = $this->getRepository()->find($id);

        if ($urlScanContent === null) {
            throw UrlScanContentNotFoundException::createForId($id);
        }

        return $urlScanContent;
    }

    /**
     * @inheritDoc
     */
    public function findByUrlScanId(int $urlScanId): iterable
    {
        return $this->getRepository()
            ->createQueryBuilder('url_scan_content')
            ->where('url_scan_content.urlScan = :url_scan_id')
            ->setParameter('url_scan_id', $urlScanId)
            ->orderBy('url_scan_content.lastFoundAt', 'DESC')
            ->getQuery()
            ->getResult();
    }

    public function findOneByUrlScanIdAndChecksum(int $urlScanId, string $checksum): ?UrlScanContent
    {
        return $this->getRepository()
            ->createQueryBuilder('url_scan_content')
            ->where('url_scan_content.urlScan = :url_scan_id')
            ->andWhere('url_scan_content.checksum = :checksum')
            ->setParameter('url_scan_id', $urlScanId)
            ->setParameter('checksum', $checksum)
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function store(UrlScanContent $urlScanContent): void
    {
        $this->entityManager->persist($urlScanContent);
        $this->entityManager->flush();
    }

    /**
     * @return EntityRepository<UrlScanContent>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(UrlScanContent::class);
    }
}
