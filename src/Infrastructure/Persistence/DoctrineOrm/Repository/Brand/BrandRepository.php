<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\Brand;

use Doctrine\DBAL\Exception\UniqueConstraintViolationException;
use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\Brand\Brand;
use Domain\Brand\BrandRepositoryInterface;
use Domain\Brand\Exception\BrandAlreadyExistsException;

final readonly class BrandRepository implements BrandRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager,
        private BrandQueryBuilderHelper $brandQueryBuilderHelper
    )
    {
    }

    public function findOneBySlug(string $slug): ?Brand
    {
        return $this->getRepository()->findOneBy(
            [
                'slug' => $slug,
            ]
        );
    }

    /**
     * @inheritDoc
     */
    public function getAllSlugs(): array
    {
        $slugs = [];

        /** @var array<array<string, string>> $result */
        $result = $this->getRepository()
            ->createQueryBuilder('brand')
            ->select('brand.id, brand.slug')
            ->orderBy('brand.slug')
            ->getQuery()
            ->getArrayResult();

        foreach ($result as $data) {
            $slugs[] = $data['slug'];
        }

        return $slugs;
    }

    public function findAll(?string $sort = null, ?string $sortDirection = null): \Generator
    {
        $order = $sortDirection === 'desc' ? 'DESC' : 'ASC';
        $orderProperty = match ($sort) {
            'active'                            => 'brand.active',
            'adsense_contract_type'             => 'brand.adSenseContractType',
            'article'                           => 'article.enabled',
            'bing_ads_approval'                 => 'brand.hasBingAdsApproval',
            'campaign_name_validation'          => 'tracking.campaignNameValidationEnabled',
            'cheq'                              => 'cheq.enabled',
            'content_page_collection'           => 'content_page.collection',
            'content_page_home_type'            => 'content_page_home.type',
            'display_search'                    => 'display_search.enabled',
            'display_search_related'            => 'display_search_related.enabled',
            'google_publisher_tag'              => 'google_publisher_tag.enabled',
            'google_publisher_tag_ad_unit_path' => 'google_publisher_tag.adUnitPath',
            'google_tag_manager'                => 'google_tag_manager.enabled',
            'google_tag_manager_id'             => 'google_tag_manager.googleTagManagerId',
            'json_template_variant'             => 'json_template.templateVariant',
            'json_template_overrides'           => 'json_template.templateOverrides',
            'last_imported_at'                  => 'brand.lastImportedAt',
            'last_pushed_at'                    => 'brand.lastPushedAt',
            'microsoft_search'                  => 'microsoft_search.enabled',
            'microsoft_search_related'          => 'microsoft_search_related.enabled',
            'organic_result_route'              => 'content_page.organicResultRoute',
            'pageview_conversion'               => 'pageview_conversion.enabled',
            'partner_slug'                      => 'brand.partnerSlug',
            'project'                           => 'brand.project',
            'search'                            => 'search.enabled',
            'search_seo'                        => 'search.seoEnabled',
            'spam_click_detect'                 => 'spam_click_detect.enabled',
            'status'                            => 'brand.status',
            'web_search'                        => 'web_search.enabled',
            default                             => 'brand.slug',
        };

        $queryBuilder = $this->getRepository()
            ->createQueryBuilder('brand')
            ->select('brand')
            ->orderBy($orderProperty, $order)
            ->addOrderBy('brand.slug', 'ASC');

        $this->brandQueryBuilderHelper
            ->addModuleSelects($queryBuilder)
            ->addModuleJoins($queryBuilder);

        $results = $queryBuilder
            ->getQuery()
            ->toIterable();

        foreach ($results as $brand) {
            yield $brand;
        }
    }

    public function findWithDisplaySearchEnabled(): \Generator
    {
        $queryBuilder = $this->getRepository()
            ->createQueryBuilder('brand')
            ->select('brand')
            ->orderBy('brand.slug', 'ASC');

        $this->brandQueryBuilderHelper
            ->addModuleSelects($queryBuilder)
            ->addModuleJoins($queryBuilder);

        $queryBuilder
            ->where('display_search_related.enabled = :enabled')
            ->setParameter('enabled', 1);

        $results = $queryBuilder
            ->getQuery()
            ->toIterable();

        foreach ($results as $brand) {
            yield $brand;
        }
    }

    public function findWithMonetization(): \Generator
    {
        $queryBuilder = $this->getRepository()
            ->createQueryBuilder('brand')
            ->select('brand')
            ->orderBy('brand.slug', 'ASC');

        $this->brandQueryBuilderHelper
            ->addModuleSelects($queryBuilder)
            ->addModuleJoins($queryBuilder);

        $queryBuilder
            ->where('monetization.adsEnabled IS NOT NULL');

        $results = $queryBuilder
            ->getQuery()
            ->toIterable();

        foreach ($results as $brand) {
            yield $brand;
        }
    }

    public function findForOrganicResults(): \Generator
    {
        $queryBuilder = $this->getRepository()
            ->createQueryBuilder('brand')
            ->select('brand')
            ->addOrderBy('brand.slug', 'ASC');

        $this->brandQueryBuilderHelper
            ->addModuleSelects($queryBuilder)
            ->addModuleJoins($queryBuilder);

        $queryBuilder->andWhere('content_page.enabled = 1')
            ->andWhere('content_page.useBrandForOrganicResults = 1')
            ->andWhere('content_page.organicResultRoute IS NOT NULL');

        $results = $queryBuilder
            ->getQuery()
            ->toIterable();

        /** @var Brand $brand */
        foreach ($results as $brand) {
            yield $brand;
        }
    }

    public function findOneActiveWithoutPartnerSlug(): ?Brand
    {
        $queryBuilder = $this->getRepository()
            ->createQueryBuilder('brand')
            ->select('brand')
            ->where('brand.partnerSlug IS NULL')
            ->where('brand.active = :active')
            ->setParameter('active', 1)
            ->orderBy('brand.slug', 'ASC')
            ->setMaxResults(1);

        $this->brandQueryBuilderHelper
            ->addModuleSelects($queryBuilder)
            ->addModuleJoins($queryBuilder);

        return $queryBuilder
            ->getQuery()
            ->getOneOrNullResult();
    }

    public function store(Brand $brand): void
    {
        try {
            $this->entityManager->persist($brand);
            $this->entityManager->flush();
        } catch (UniqueConstraintViolationException $exception) {
            throw BrandAlreadyExistsException::create($brand->slug, $exception);
        }
    }

    public function delete(Brand $brand): void
    {
        $this->entityManager->remove($brand);
        $this->entityManager->flush();
    }

    /**
     * @return EntityRepository<Brand>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(Brand::class);
    }
}
