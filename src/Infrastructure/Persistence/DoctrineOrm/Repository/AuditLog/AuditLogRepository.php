<?php

declare(strict_types=1);

namespace Infrastructure\Persistence\DoctrineOrm\Repository\AuditLog;

use Doctrine\ORM\EntityManagerInterface;
use Doctrine\ORM\EntityRepository;
use Domain\AuditLog\AuditLog;
use Domain\AuditLog\AuditLogRepositoryInterface;
use Domain\Generic\Sort;
use Symfony\Component\PropertyAccess\Exception\InvalidArgumentException;

final readonly class AuditLogRepository implements AuditLogRepositoryInterface
{
    public function __construct(
        private EntityManagerInterface $entityManager
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function findByUserId(int $userId, Sort $sort = Sort::DESCENDING, int $limit = 50): array
    {
        return $this->getRepository()
            ->createQueryBuilder('audit_log')
            ->orderBy('audit_log.changedAt', $sort->value)
            ->where('audit_log.user = :user')
            ->setParameter('user', $userId)
            ->setMaxResults($limit)
            ->getQuery()
            ->getResult();
    }

    public function findLatestByObjectIdAndType(int $objectId, string $objectType): ?AuditLog
    {
        return $this->getRepository()
            ->createQueryBuilder('audit_log')
            ->where('audit_log.objectId = :objectId')
            ->andWhere('audit_log.objectType LIKE :objectType')
            ->setParameter('objectId', $objectId)
            ->setParameter('objectType', sprintf('%s%%', $objectType))
            ->orderBy('audit_log.changedAt', Sort::DESCENDING->value)
            ->setMaxResults(1)
            ->getQuery()
            ->getOneOrNullResult();
    }

    /**
     * @inheritDoc
     */
    public function findWithFilters(
        int $objectId,
        array $objectTypes,
        ?\DateTime $startDate,
        ?\DateTime $endDate,
        Sort $sort = Sort::DESCENDING,
        int $limit = 50
    ): array
    {
        if ($objectTypes === []) {
            throw new InvalidArgumentException('objectTypes should not be empty.');
        }

        $queryBuilder = $this->getRepository()->createQueryBuilder('audit_log');

        $counter = 1;
        $likeConditions = [];

        foreach ($objectTypes as $objectType) {
            $parameterName = sprintf('objectType%d', $counter++);
            $likeConditions[] = sprintf('audit_log.objectType LIKE :%s', $parameterName);
            $queryBuilder->setParameter($parameterName, sprintf('%%%s%%', $objectType));
        }

        $likeClause = implode(' OR ', $likeConditions);

        $queryBuilder
            ->where('audit_log.objectId = :objectId')
            ->setParameter('objectId', $objectId)
            ->andWhere($likeClause);

        if ($startDate !== null) {
            $queryBuilder
                ->andWhere('audit_log.changedAt >= :start_date')
                ->setParameter('start_date', $startDate->format('Y-m-d'));
        }

        if ($endDate !== null) {
            $queryBuilder
                ->andWhere('audit_log.changedAt <= :end_date')
                ->setParameter('end_date', $endDate->format('Y-m-d 23:59:59'));
        }

        $queryBuilder
            ->setMaxResults($limit)
            ->orderBy('audit_log.changedAt', $sort->value);

        return $queryBuilder->getQuery()->getResult();
    }

    public function store(AuditLog $auditLog): void
    {
        $this->entityManager->persist($auditLog);
        $this->entityManager->flush();
    }

    /**
     * @return EntityRepository<AuditLog>
     */
    private function getRepository(): EntityRepository
    {
        return $this->entityManager->getRepository(AuditLog::class);
    }

    public function removeByObjectIdAndType(int $objectId, string $objectType): void
    {
        $auditLogs = $this->getRepository()
            ->createQueryBuilder('audit_log')
            ->where('audit_log.objectId = :object_id')
            ->andWhere('audit_log.objectType LIKE :object_type')
            ->setParameter('object_id', $objectId)
            ->setParameter('object_type', sprintf('%s%%', $objectType))
            ->getQuery()
            ->getResult();

        foreach ($auditLogs as $auditLog) {
            $this->entityManager->remove($auditLog);
        }

        $this->entityManager->flush();
    }
}
