<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Url\Persistent;

use Infrastructure\UserInterface\Web\Request\ToggleRequest;

final readonly class PersistentToggleParametersProvider implements PersistentUrlParametersProviderInterface
{
    public function __construct(
        private ToggleRequest $toggleRequest
    ) {
    }

    /**
     * @inheritDoc
     */
    public function getPersistentUrlParameters(): array
    {
        $parameters = [];

        if (!$this->toggleRequest->isActive()) {
            $parameters[ToggleRequest::PARAMETER_TOGGLE] = '0';
        }

        return $parameters;
    }
}
