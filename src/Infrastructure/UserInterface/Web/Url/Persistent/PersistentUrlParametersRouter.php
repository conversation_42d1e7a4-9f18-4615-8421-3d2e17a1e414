<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Url\Persistent;

use Infrastructure\UserInterface\Web\Helper\DevelopHostHelper;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Routing\RouterInterface;

readonly class PersistentUrlParametersRouter
{
    public function __construct(
        private RouterInterface $router,
        private DevelopHostHelper $developHostHelper,
        private PersistentUrlParametersHelper $persistentUrlParametersHelper
    )
    {
    }

    /**
     * @param array<string, int|string|null> $routeParameters
     */
    public function generate(
        string $route,
        array $routeParameters = [],
        bool $absoluteUrl = false
    ): string
    {
        $urlParameters = [
            ...$this->persistentUrlParametersHelper->getPersistentParameters(),
            ...$routeParameters,
        ];
        $referenceType = $absoluteUrl
            ? UrlGeneratorInterface::ABSOLUTE_URL
            : UrlGeneratorInterface::ABSOLUTE_PATH;

        return $this->router->generate($route, $urlParameters, $referenceType);
    }

    /**
     * @param array<string, int|string|null> $routeParameters
     */
    public function generateForDomain(
        string $domain,
        string $route,
        array $routeParameters = []
    ): string
    {
        $path = $this->generate($route, $routeParameters);
        $domain = $this->developHostHelper->addDevelopToHost($domain);

        return sprintf('https://%s%s', $domain, $path);
    }

    /**
     * @param array<string, int|string|null> $routeParameters
     */
    public function redirectToRoute(
        string $route,
        array $routeParameters = [],
        int $status = Response::HTTP_FOUND
    ): RedirectResponse
    {
        $urlParameters = [
            ...$this->persistentUrlParametersHelper->getPersistentParameters(),
            ...$routeParameters,
        ];
        $redirectUrl = $this->router->generate($route, $urlParameters, UrlGeneratorInterface::ABSOLUTE_URL);

        return new RedirectResponse($redirectUrl, $status);
    }
}
