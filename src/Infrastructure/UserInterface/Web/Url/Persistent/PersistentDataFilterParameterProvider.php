<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Url\Persistent;

use Infrastructure\UserInterface\Web\Request\DataFilterRequest;

final readonly class PersistentDataFilterParameterProvider implements PersistentUrlParametersProviderInterface
{
    public function __construct(
        private DataFilterRequest $dataFilterRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getPersistentUrlParameters(): array
    {
        $parameters = [];
        $expression = $this->dataFilterRequest->getExpression();

        if ($expression !== null) {
            $parameters[DataFilterRequest::PARAMETER_EXPRESSION] = $expression;
        }

        return $parameters;
    }
}
