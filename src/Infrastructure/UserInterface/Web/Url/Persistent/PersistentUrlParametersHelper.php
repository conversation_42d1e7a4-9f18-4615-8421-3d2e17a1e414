<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Url\Persistent;

class PersistentUrlParametersHelper
{
    private bool $enabled = true;

    /** @var PersistentUrlParametersProviderInterface[] */
    private array $parameterProviders = [];

    /** @var array<string, string|null> */
    private array $persistentParameters = [];

    /**
     * @param PersistentUrlParametersProviderInterface[] $parameterProviders
     */
    public function __construct(iterable $parameterProviders)
    {
        foreach ($parameterProviders as $parameterProvider) {
            $this->addParameterProvider($parameterProvider);
        }
    }

    public function disable(): void
    {
        $this->enabled = false;
    }

    private function addParameterProvider(PersistentUrlParametersProviderInterface $parameterProvider): void
    {
        $this->parameterProviders[] = $parameterProvider;
    }

    /**
     * @return array<string, string|null>
     */
    public function getPersistentParameters(): array
    {
        if (!$this->enabled) {
            return [];
        }

        if ($this->persistentParameters !== []) {
            return $this->persistentParameters;
        }

        $parameterStacks = [];

        foreach ($this->parameterProviders as $parameterProvider) {
            $parameterStacks[] = $parameterProvider->getPersistentUrlParameters();
        }

        return $this->persistentParameters = array_merge(...$parameterStacks);
    }
}
