<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Generic;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\AdSenseStyleId\Exception\InvalidAdSenseStyleIdException;
use Domain\Brand\ContractType\AdSenseContractType;
use Domain\BrandModule\BrandModuleInterface;
use Infrastructure\UserInterface\Web\Form\Brand\Transformer\AdSenseStyleIdTransformer;
use Infrastructure\UserInterface\Web\Form\Validator\AdSenseStyleIdConstraint;
use Symfony\Component\Form\Extension\Core\Type\TextType;
use Symfony\Component\Form\Form;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormEvent;
use Symfony\Component\Form\FormEvents;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class AdSenseStyleIdType extends TextType
{
    /**
     * @inheritDoc
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        $this->setPostSubmitListener($builder);
        $builder->setAttribute('disabled', false);
        $builder->addModelTransformer(new AdSenseStyleIdTransformer());
        $this->setPreSetDataListener($builder);

        parent::buildForm($builder, $options);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        parent::configureOptions($resolver);

        $resolver->setDefaults(
            [
                'attr'        => [
                    'pattern'   => '^[1-9][0-9]{9}$',
                    'maxlength' => 10,
                ],
                'constraints' => [
                    new AdSenseStyleIdConstraint(),
                ],
                'required'    => false,
                'setter'      => static function ($data, ?string $value, Form $form): void {
                    $field = $form->getName();

                    if (!$data instanceof BrandModuleInterface) {
                        return;
                    }

                    if ($value === null) {
                        // @phpstan-ignore-next-line
                        $data->{$field} = null;

                        return;
                    }

                    /**
                     * @var AdSenseStyleId|null $currentStyleId
                     *
                     * @phpstan-ignore-next-line
                     */
                    $currentStyleId = $data->{$field} ?? null;

                    try {
                        $styleId = AdSenseStyleId::fromStyleId($value);
                    } catch (InvalidAdSenseStyleIdException) {
                        return;
                    }

                    if ($currentStyleId !== null && $currentStyleId->equals($styleId)) {
                        return;
                    }

                    // @phpstan-ignore-next-line
                    $data->{$field} = $styleId;
                },
            ]
        );
    }

    public function getBlockPrefix(): string
    {
        return 'adsense_style_id';
    }

    private function setPostSubmitListener(FormBuilderInterface $builder): void
    {
        $builder->addEventListener(
            FormEvents::POST_SUBMIT,
            static function (FormEvent $event): void {
                $form = $event->getForm()->getParent();
                $fieldName = $event->getForm()->getName();

                if ($form === null || !(bool)$form->get('enabled')->getData()) {
                    return;
                }

                foreach ($form->all() as $name => $child) {
                    $config = $child->getConfig();
                    $value = $child->getData();

                    if ($name !== $fieldName ||
                        $value !== null ||
                        !$config->getType()->getInnerType() instanceof self
                    ) {
                        continue;
                    }

                    $child->addError(
                        new FormError(
                            sprintf('%s field is required because the module is enabled.', $name)
                        )
                    );
                }
            }
        );
    }

    private function setPreSetDataListener(FormBuilderInterface $builder): void
    {
        $builder->addEventListener(
            FormEvents::PRE_SET_DATA,
            static function (FormEvent $event): void {
                $data = $event->getData();
                $form = $event->getForm();
                $parent = $form->getParent();
                $parentEntity = $parent?->getData();

                if ($parent === null ||
                    !$data instanceof AdSenseStyleId ||
                    !$parentEntity instanceof BrandModuleInterface
                ) {
                    return;
                }

                // Prevent recursion: do not re-add if already disabled
                if ((bool)$form->getConfig()->getOption('disabled')) {
                    return;
                }

                $parentEntity = $parent->getData();
                $shouldDisable = $parentEntity instanceof BrandModuleInterface
                                 && $parentEntity->getBrand()->adSenseContractType === AdSenseContractType::ONLINE;

                if ($shouldDisable) {
                    $parent->add(
                        $form->getName(),
                        self::class,
                        [
                            'disabled' => true,
                        ]
                    );
                }
            }
        );
    }
}
