<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\JsonTemplate\JsonTemplate;
use Infrastructure\Service\JsonTemplateOverrides\Exception\DuplicateTemplateOverridesException;
use Infrastructure\Service\JsonTemplateOverrides\Exception\JsonTemplateOverrideDoesNotExistsException;
use Infrastructure\Service\JsonTemplateOverrides\JsonTemplateOverrides;
use Infrastructure\Service\JsonTemplateOverrides\Validator\JsonTemplateOverridesValidator;
use Symfony\Component\Form\CallbackTransformer;
use Symfony\Component\Form\Extension\Core\Type\ChoiceType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormError;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;
use Symfony\Component\Validator\Constraints\Length;
use Symfony\Component\Validator\Constraints\Regex;

final class ModuleJsonTemplateType extends AbstractBrandModuleType
{
    private const string FIELD_TEMPLATE_VARIANT   = 'templateVariant';
    private const string FIELD_TEMPLATE_OVERRIDES = 'templateOverrides';

    public function __construct(
        private readonly JsonTemplateOverrides $jsonTemplateOverrides,
        private readonly JsonTemplateOverridesValidator $jsonTemplateOverridesValidator
    )
    {
    }

    public static function getBrandModuleClass(): string
    {
        return JsonTemplate::class;
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(
                self::FIELD_TEMPLATE_VARIANT,
                null,
                [
                    'attr'        => [
                        'pattern'   => '^[a-z]+$',
                        'maxlength' => 10,
                    ],
                    'constraints' => [
                        new Length(['max' => 10]),
                        new Regex(['pattern' => '/^[a-z]+$/'], 'Template variant must be alphanumeric.'),
                    ],
                ]
            )
            ->add(
                self::FIELD_TEMPLATE_OVERRIDES,
                ChoiceType::class,
                [
                    'multiple'     => true,
                    'choices'      => $this->jsonTemplateOverrides->getAllOverrideNames(),
                    'choice_label' => static fn (string $templateOverride): string => $templateOverride,
                    'attr'         => [
                        'size' => 10,
                    ],
                ]
            );

        $callbackTransformer = new CallbackTransformer(
            static fn (?string $templateOverrides): ?array => $templateOverrides === null ? null : explode(',', $templateOverrides),
            static fn (?array $templateOverrides): ?string => (array)$templateOverrides === [] ? null : implode(',', $templateOverrides)
        );

        $builder->get(self::FIELD_TEMPLATE_OVERRIDES)
            ->addModelTransformer($callbackTransformer);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => static function (FormInterface $form): ?JsonTemplate {
                    $templateVariant = $form->get(self::FIELD_TEMPLATE_VARIANT)->getData();
                    $templateOverrides = $form->get(self::FIELD_TEMPLATE_OVERRIDES)->getData();

                    if ($templateVariant === null && $templateOverrides === null) {
                        return null;
                    }

                    return new JsonTemplate(
                        templateVariant  : $templateVariant,
                        templateOverrides: $templateOverrides
                    );
                },
                'setter'     => static function (Brand $brand, ?JsonTemplate $jsonTemplate): void {
                    $brand->setJsonTemplate($jsonTemplate);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        $templateOverridesField = $form->get(self::FIELD_TEMPLATE_OVERRIDES);
        $templateOverridesData = $templateOverridesField->getData() ?? '';
        $templateOverrides = $templateOverridesData === '' ? [] : explode(',', $templateOverridesData);

        try {
            $this->jsonTemplateOverridesValidator->validate($templateOverrides);
        } catch (DuplicateTemplateOverridesException|JsonTemplateOverrideDoesNotExistsException $exception) {
            $templateOverridesField->addError(
                new FormError($exception->getMessage())
            );
        }
    }
}
