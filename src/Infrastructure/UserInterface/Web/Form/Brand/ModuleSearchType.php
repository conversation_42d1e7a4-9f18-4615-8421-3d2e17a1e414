<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Brand;

use Domain\Brand\Brand;
use Domain\Search\Search;
use Infrastructure\UserInterface\Web\Form\Generic\AdSenseStyleIdType;
use Symfony\Component\Form\FormBuilderInterface;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\OptionsResolver\OptionsResolver;

final class ModuleSearchType extends AbstractBrandModuleType
{
    private const string FIELD_SEO_ENABLED      = 'seoEnabled';
    private const string FIELD_STYLE_ID_DESKTOP = 'styleIdDesktop';
    private const string FIELD_STYLE_ID_MOBILE  = 'styleIdMobile';

    public static function getBrandModuleClass(): string
    {
        return Search::class;
    }

    /**
     * @param mixed[] $options
     */
    public function buildForm(FormBuilderInterface $builder, array $options): void
    {
        parent::buildForm($builder, $options);

        $builder
            ->add(self::FIELD_SEO_ENABLED)
            ->add(self::FIELD_STYLE_ID_DESKTOP, AdSenseStyleIdType::class)
            ->add(self::FIELD_STYLE_ID_MOBILE, AdSenseStyleIdType::class);
    }

    public function configureOptions(OptionsResolver $resolver): void
    {
        $resolver->setDefaults(
            [
                'data_class' => self::getBrandModuleClass(),
                'empty_data' => fn (FormInterface $form): Search => new Search(
                    $this->isEnabled($form),
                    (bool)$form->get(self::FIELD_SEO_ENABLED)->getData(),
                    $form->get(self::FIELD_STYLE_ID_DESKTOP)->getData(),
                    $form->get(self::FIELD_STYLE_ID_MOBILE)->getData()
                ),
                'setter'     => static function (Brand $brand, ?Search $search): void {
                    $brand->setSearch($search);
                },
            ]
        );
    }

    public function validateData(FormInterface $form): void
    {
        // No additional validation needed.
    }
}
