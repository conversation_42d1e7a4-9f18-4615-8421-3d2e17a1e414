<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Form\Validator;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\AdSenseStyleId\AdSenseStyleIdRegistryChecker;
use Domain\AdSenseStyleId\AdSenseStyleIdStatus;
use Domain\AdSenseStyleId\Exception\InvalidAdSenseStyleIdException;
use Domain\Brand\Brand;
use Domain\Brand\ContractType\AdSenseContractType;
use Infrastructure\UserInterface\Web\Form\MessageResolver\AdSenseStyleIdValidationMessageResolver;
use Symfony\Component\Form\Form;
use Symfony\Component\Validator\Constraint;
use Symfony\Component\Validator\ConstraintValidator;
use Symfony\Component\Validator\Exception\UnexpectedTypeException;
use Symfony\Component\Validator\Exception\UnexpectedValueException;

final class AdSenseStyleIdConstraintValidator extends ConstraintValidator
{
    public function __construct(
        private readonly AdSenseStyleIdRegistryChecker $styleIdValidator,
        private readonly AdSenseStyleIdValidationMessageResolver $styleIdValidationMessageResolver
    )
    {
    }

    public function validate(mixed $value, Constraint $constraint): void
    {
        if (!$constraint instanceof AdSenseStyleIdConstraint) {
            throw new UnexpectedTypeException($constraint, AdSenseStyleIdConstraint::class);
        }

        /** @var Form $form */
        $form = $this->context->getObject();
        $brandModuleData = $form->getParent()?->getData();

        if ($brandModuleData === null || $value === null || $value === '') {
            return;
        }

        /** @var Brand $brand */
        $brand = $brandModuleData->brand;

        if (!is_string($value)) {
            throw new UnexpectedValueException($value, 'string');
        }

        try {
            $status = $this->styleIdValidator->checkStatus(AdSenseStyleId::fromStyleId($value), $brand->slug);

            // TODO If the contract type is DIRECT, we allow duplicate style IDs, remove in SF-1218
            if ($brand->adSenseContractType === AdSenseContractType::DIRECT &&
                $status === AdSenseStyleIdStatus::NOT_UNIQUE
            ) {
                return;
            }

            $message = $this->styleIdValidationMessageResolver->resolve($status, $value);

            if ($message === null) {
                return;
            }
        } catch (InvalidAdSenseStyleIdException $exception) {
            $message = $exception->getMessage();
        }

        $this->context->buildViolation($message)
            ->setParameter('{{ value }}', $value)
            ->addViolation();
    }
}
