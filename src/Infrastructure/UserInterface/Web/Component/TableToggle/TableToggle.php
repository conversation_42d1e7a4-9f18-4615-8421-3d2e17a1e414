<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\TableToggle;

use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Infrastructure\UserInterface\Web\Component\ComponentUrlParametersInterface;
use Infrastructure\UserInterface\Web\Request\ToggleRequest;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/TableToggle/table_toggle.html.twig')]
final class TableToggle implements ComponentInterface, ComponentUrlParametersInterface
{
    public string $title = 'Toggle table columns';

    public function __construct(
        private readonly ToggleRequest $toggleRequest
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getUrlParameters(): array
    {
        return [
            ToggleRequest::PARAMETER_TOGGLE => $this->isActive() ? '0' : null,
        ];
    }

    public function isActive(): bool
    {
        return $this->toggleRequest->isActive();
    }

    public function getComponentCssClass(): string
    {
        return 'table-toggle';
    }
}
