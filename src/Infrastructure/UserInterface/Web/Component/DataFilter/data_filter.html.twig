{{ component_script('dataFilter') }}
{{ component_style('dataFilter') }}
{% set component_class = this.getComponentCssClass() %}
<form action="{{ current_route() }}" method="GET" class="{{ component_class }}" data-collection="{{ collection }}">
    <div class="{{ component_class }}__search">
        <input type="search" name="expression" class="form__control {{ component_class }}__input" autocomplete="off" placeholder="{{ placeholder }}" value="{{ expression }}"/>
        <div class="{{ component_class }}__suggestions"></div>
    </div>
    <div class="{{ component_class }}__submit">
        <twig:Button submit="true">Update</twig:Button>
    </div>
    {% if error is not null %}
        <div class="{{ component_class }}__error">{{- error -}}</div>
    {% endif %}
    {{ persistent_path_input_fields(['expression']) }}
</form>
