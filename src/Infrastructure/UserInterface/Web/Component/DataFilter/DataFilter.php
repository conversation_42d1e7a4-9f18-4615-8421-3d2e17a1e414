<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Component\DataFilter;

use Infrastructure\Service\DataFilter\Matcher\DataFilterMatcherInterface;
use Infrastructure\UserInterface\Web\Component\ComponentInterface;
use Symfony\UX\TwigComponent\Attribute\AsTwigComponent;

#[AsTwigComponent(template: '@component/DataFilter/data_filter.html.twig')]
final class DataFilter implements ComponentInterface
{
    public string $placeholder = 'Filter';

    public string $collection;

    public string $error;

    public string $expression;

    public function mount(DataFilterMatcherInterface $dataFilterMatcher): void
    {
        $this->collection = $dataFilterMatcher->getCollection()->value;
        $this->error = (string)$dataFilterMatcher->getError();
        $this->expression = (string)$dataFilterMatcher->getExpression();
    }

    public function getComponentCssClass(): string
    {
        return 'data-filter';
    }
}
