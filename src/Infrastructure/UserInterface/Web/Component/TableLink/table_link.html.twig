{{ component_style('table-link') }}
{% set is_granted = is_granted_for_url(url) %}
{% set class = 'table-link' ~ (modifier is not null ? ' table-link--' ~ modifier) %}
{%- if is_granted -%}
    <a class="{{ class }}" href="{{ url }}" target="{{ target }}" title="{{ title }}">
{%- else -%}
    <span class="{{ class }} table-link--disabled">
{%- endif -%}
{%- block content -%}{%- endblock -%}
{{ is_granted ? '</a>' : '</span>' }}
