<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Menu\Model;

final readonly class MenuItem
{
    private const string KEY_TEXT          = 'text';
    private const string KEY_ICON          = 'icon';
    private const string KEY_URL           = 'url';
    private const string KEY_ACTIVE        = 'active';
    private const string KEY_SUBMENU_ITEMS = 'submenu_items';

    /**
     * @param SubmenuItem[] $submenuItems
     */
    public function __construct(
        public string $text,
        public string $icon,
        public string $url,
        public bool $active,
        public array $submenuItems
    )
    {
    }

    /**
     * @return mixed[]
     */
    public function toArray(): array
    {
        return [
            self::KEY_TEXT          => $this->text,
            self::KEY_ICON          => $this->icon,
            self::KEY_URL           => $this->url,
            self::KEY_ACTIVE        => $this->active,
            self::KEY_SUBMENU_ITEMS => array_map(
                static fn (SubmenuItem $submenuItem) => $submenuItem->toArray(),
                $this->submenuItems
            ),
        ];
    }
}
