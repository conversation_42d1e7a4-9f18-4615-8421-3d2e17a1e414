<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Twig;

use Infrastructure\UserInterface\Web\Request\Info\RequestInfoInterface;
use Infrastructure\UserInterface\Web\Url\Persistent\PersistentUrlParametersHelper;
use Infrastructure\UserInterface\Web\Url\Persistent\PersistentUrlParametersRouter;
use Symfony\Component\Routing\RouterInterface;
use Twig\Environment;
use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;

final class UrlExtension extends AbstractExtension
{
    public function __construct(
        private readonly PersistentUrlParametersRouter $persistentUrlParametersRouter,
        private readonly PersistentUrlParametersHelper $persistentUrlParametersHelper,
        private readonly RequestInfoInterface $requestInfo,
        private readonly Environment $twig,
        private readonly RouterInterface $router
    )
    {
    }

    /**
     * @inheritDoc
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'current_route',
                $this->getCurrentRoute(...),
            ),
            new TwigFunction(
                'persistent_path_current_route',
                $this->generatePersistentPathCurrentRoute(...),
            ),
            new TwigFunction(
                'persistent_path_input_fields',
                $this->getPersistentPathInputFields(...),
                ['is_safe' => ['html']]
            ),
        ];
    }

    public function getCurrentRoute(): string
    {
        $route = $this->requestInfo->getRoute();

        return $this->router->generate($route);
    }

    /**
     * @param array<string, mixed> $parameters
     */
    public function generatePersistentPathCurrentRoute(array $parameters = []): string
    {
        $route = $this->requestInfo->getRoute();

        return $this->persistentUrlParametersRouter->generate($route, $parameters, true);
    }

    /**
     * @param array<string, string|null> $exceptParameters
     */
    public function getPersistentPathInputFields(
        array $exceptParameters = []
    ): string
    {
        $inputFields = [...$this->persistentUrlParametersHelper->getPersistentParameters()];
        $inputFields = $this->arrayRemoveNullValues($inputFields);

        foreach ($exceptParameters as $parameter) {
            unset($inputFields[$parameter]);
        }

        return $this->twig->render(
            'form/_persistent_path_input_fields.html.twig',
            [
                'inputFields' => $inputFields,
            ],
        );
    }

    /**
     * @param mixed[] $array
     *
     * @return mixed[]
     */
    private function arrayRemoveNullValues(array $array): array
    {
        return array_filter($array, static fn (?string $value) => $value !== null);
    }
}
