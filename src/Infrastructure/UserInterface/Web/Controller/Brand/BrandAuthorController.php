<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller\Brand;

use Application\Query\Brand\GetAuthorsQuery\GetAuthorsQuery;
use Application\Query\Brand\GetAuthorsQuery\GetAuthorsResponse;
use Domain\Author\Author;
use Domain\Author\AuthorFactory;
use Domain\Author\AuthorRepositoryInterface;
use Domain\Author\Exception\AuthorNotFoundException;
use Domain\User\Security\SecurityRole;
use Infrastructure\Service\File\UploadedFileException;
use Infrastructure\Service\File\UploadedFileResolver;
use Infrastructure\UserInterface\Web\FlashMessage\FlashMessageRepository;
use Infrastructure\UserInterface\Web\Form\Author\AuthorType;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\FormInterface;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Visymo\QueryBus\QueryBus\QueryBusInterface;

final class BrandAuthorController extends AbstractController
{
    public function __construct(
        private readonly AuthorRepositoryInterface $authorRepository,
        private readonly AuthorFactory $authorFactory,
        private readonly QueryBusInterface $queryBus,
        private readonly UploadedFileResolver $uploadedFileResolver,
        private readonly FlashMessageRepository $flashMessageRepository
    )
    {
    }

    #[Route(
        path   : '/brand/authors',
        name   : 'route_brand_author_index',
        methods: ['GET'],
    )]
    #[IsGranted(SecurityRole::USER->value)]
    public function authors(): Response
    {
        /** @var GetAuthorsResponse $response */
        $response = $this->queryBus->handle(new GetAuthorsQuery());

        return $this->render(
            'brand/author/index.html.twig',
            [
                'authors' => $response->results,
            ]
        );
    }

    #[Route(
        path   : '/brand/authors/create',
        name   : 'route_brand_author_create',
        methods: ['GET', 'POST'],
    )]
    #[Route(
        path   : '/brand/authors/{slug}/edit',
        name   : 'route_brand_author_edit',
        methods: ['GET', 'POST'],
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function form(?string $slug, Request $request): Response
    {
        $author = $slug !== null
            ? $this->authorRepository->findOneBySlug($slug)
            : $this->authorFactory->createNew();

        if ($author === null) {
            throw AuthorNotFoundException::createForSlug($slug ?? 'unknown');
        }

        $form = $this->createForm(AuthorType::class, $author);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            if (!$this->handleImageUpload($form, $author)) {
                $this->flashMessageRepository->addWarning('Failed to process image');
            }

            $this->authorRepository->store($author);
            $this->flashMessageRepository->addSuccess(sprintf('Author "%s" has been saved', $author->name));

            return $this->redirectToRoute('route_brand_author_index');
        }

        return $this->render(
            'brand/author/form.html.twig',
            [
                'form'  => $form,
                'title' => $slug === null ? 'Create author' : 'Edit author',
            ]
        );
    }

    #[Route(
        path   : '/authors/{slug}/delete',
        name   : 'route_brand_author_delete',
        methods: ['GET'],
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function delete(string $slug): Response
    {
        $author = $this->authorRepository->findOneBySlug($slug);

        if ($author === null) {
            throw AuthorNotFoundException::createForSlug($slug);
        }

        $this->authorRepository->delete($author);
        $this->flashMessageRepository->addSuccess(sprintf('Author "%s" has been deleted', $author->name));

        return $this->redirectToRoute('route_brand_author_index');
    }

    private function handleImageUpload(FormInterface $form, Author $author): bool
    {
        try {
            $image = $this->uploadedFileResolver->resolveToBase64(
                $form->get('image')->getData()
            );

            if ($image !== null) {
                $author->image = $image;
            }

            return true;
        } catch (UploadedFileException) {
            $this->flashMessageRepository->addWarning('Failed to process image');

            return false;
        }
    }
}
