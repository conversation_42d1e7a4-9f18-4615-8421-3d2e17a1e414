<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller\Brand;

use Domain\AntelopeBrand\AntelopeBrand;
use Domain\AntelopeBrand\AntelopeBrandRepositoryInterface;
use Domain\Brand\Brand;
use Domain\User\Security\SecurityRole;
use Infrastructure\Persistence\GoogleAdSenseApproval\GoogleAdSenseApprovalKeywords;
use Infrastructure\UserInterface\Web\FlashMessage\FlashMessageRepository;
use Infrastructure\UserInterface\Web\Helper\DevelopHostHelper;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Visymo\Shared\Domain\Locale\Locale;

final class BrandGoogleAdSenseApprovalController extends AbstractController
{
    public function __construct(
        private readonly AntelopeBrandRepositoryInterface $antelopeBrandRepository,
        private readonly DevelopHostHelper $developHostHelper,
        private readonly FlashMessageRepository $flashMessageRepository
    )
    {
    }

    #[Route(
        path        : '/brand-google-adsense-approval/{slug}',
        name        : 'route_brand_google_adsense_approval',
        requirements: ['slug' => Brand::SLUG_REGEX],
        methods     : ['GET']
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function index(string $slug): Response
    {
        $antelopeBrand = $this->antelopeBrandRepository->findOneBySlug($slug);

        if ($antelopeBrand === null) {
            throw $this->createNotFoundException();
        }

        $response = $this->renderForLocale(Locale::NL_NL, GoogleAdSenseApprovalKeywords::getNlNlKeywords(), $antelopeBrand) ??
                    $this->renderForLocale(Locale::EN_US, GoogleAdSenseApprovalKeywords::getEnUsKeywords(), $antelopeBrand);

        if ($response !== null) {
            return $response;
        }

        $this->flashMessageRepository->addWarning(
            sprintf('Could not find a useful domain for the brand %s', $slug)
        );

        return $this->redirectToRoute('route_brand_index');
    }

    /**
     * @param string[] $keywords
     */
    private function renderForLocale(string $locale, array $keywords, AntelopeBrand $antelopeBrand): ?Response
    {
        $renderDomain = null;

        foreach ($antelopeBrand->config['domains'] ?? [] as $domain => $domainConfig) {
            $locales = $domainConfig['locales'] ?? [];

            foreach ($locales as $localConfig) {
                if ($localConfig['locale'] === $locale) {
                    $renderDomain = $domain;

                    break;
                }
            }
        }

        if ($renderDomain === null) {
            return null;
        }

        $url = sprintf('https://%s/search?q={query}&debug_info=1', $renderDomain);
        $url = $this->developHostHelper->addDevelopToUrl($url);

        return $this->render(
            'google_adsense_approval/google_adsense_approval.html.twig',
            [
                'url'              => $url,
                'keywords'         => $keywords,
                'open_page_amount' => 25,
            ]
        );
    }
}
