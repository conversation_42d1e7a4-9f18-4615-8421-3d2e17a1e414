<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Controller\Brand;

use Application\Command\Brand\PushBrandConfig\PushBrandConfigCommand;
use Application\Command\StyleId\PushBrandStyleIds\PushBrandStyleIdsCommand;
use Domain\AuditLog\AuditLogAction;
use Domain\AuditLog\AuditLogRepositoryInterface;
use Domain\Brand\Brand;
use Domain\Brand\BrandFactory;
use Domain\Brand\BrandRepositoryInterface;
use Domain\BrandModule\AdSenseStyleId\BrandModuleAdSenseStyleIdReader;
use Domain\User\Security\SecurityRole;
use Infrastructure\Service\DataFilter\Collection\DataFilterCollection;
use Infrastructure\Service\DataFilter\Matcher\DataFilterMatcherFactory;
use Infrastructure\UserInterface\Web\FlashMessage\FlashMessageRepository;
use Infrastructure\UserInterface\Web\Form\Brand\BrandSlugType;
use Infrastructure\UserInterface\Web\Form\Brand\BrandType;
use Infrastructure\UserInterface\Web\Request\AuditLogRequest;
use Infrastructure\UserInterface\Web\Request\DataFilterRequest;
use Infrastructure\UserInterface\Web\Request\SortRequest;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\Form\SubmitButton;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Symfony\Component\Security\Http\Attribute\IsGranted;
use Visymo\CommandBus\Domain\CommandBus\CommandBusInterface;
use Visymo\Shared\Domain\DateTime\DateTimeFactory;
use Visymo\Shared\Domain\DateTime\TimezoneEnum;

final class BrandController extends AbstractController
{
    private const int DEFAULT_DAYS_AGO = 7;

    public function __construct(
        private readonly BrandRepositoryInterface $brandRepository,
        private readonly CommandBusInterface $commandBus,
        private readonly DateTimeFactory $dateTimeFactory,
        private readonly BrandFactory $brandFactory,
        private readonly DataFilterRequest $dataFilterRequest,
        private readonly DataFilterMatcherFactory $dataFilterMatcherFactory,
        private readonly FlashMessageRepository $flashMessageRepository,
        private readonly AuditLogRepositoryInterface $auditLogRepository
    )
    {
    }

    #[Route(
        path   : '/brand',
        name   : 'route_brand_index',
        methods: ['GET']
    )]
    public function index(SortRequest $sortRequest): Response
    {
        $dataFilterMatcher = $this->dataFilterMatcherFactory->create(
            expression: $this->dataFilterRequest->getExpression(),
            collection: DataFilterCollection::BRAND,
        );

        $brands = $this->brandRepository->findAll(
            sort         : $sortRequest->getSort(),
            sortDirection: $sortRequest->getSortDirection()
        );
        $brands = $dataFilterMatcher->filter($brands);

        return $this->render(
            'brand/index.html.twig',
            [
                'brands'              => $brands,
                'data_filter_matcher' => $dataFilterMatcher,
            ]
        );
    }

    #[Route(
        path   : '/brand/create',
        name   : 'route_brand_create',
        methods: ['GET', 'POST'],
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function create(Request $request): Response
    {
        $brand = $this->brandFactory->createEmpty();
        $form = $this->createForm(BrandSlugType::class, $brand);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->brandRepository->store($brand);

            $this->flashMessageRepository->addSuccess(sprintf('Brand "%s" has been created', $brand->slug));

            return $this->redirectToRoute('route_brand_index');
        }

        return $this->render(
            'brand/create.html.twig',
            [
                'form' => $form,
            ]
        );
    }

    #[Route(
        path        : '/brand/{slug:brand}/edit',
        name        : 'route_brand_edit',
        requirements: ['slug' => Brand::SLUG_REGEX],
        methods     : ['GET', 'POST'],
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function editBrand(
        Brand $brand,
        Request $request,
        BrandModuleAdSenseStyleIdReader $brandModuleAdSenseStyleIdReader
    ): Response
    {
        $form = $this->createForm(BrandType::class, $brand);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->brandRepository->store($brand);

            if (!$brand->isModified()) {
                $this->flashMessageRepository->addNotice('Brand has no changes');

                return $this->redirectToRoute(
                    'route_brand_edit',
                    [
                        'slug' => $brand->slug,
                    ]
                );
            }

            $this->commandBus->handle(
                new PushBrandConfigCommand($brand->slug)
            );
            $this->commandBus->handle(
                new PushBrandStyleIdsCommand($brand->slug)
            );

            $this->flashMessageRepository->addSuccess('Brand has been updated');

            return $this->redirectToRoute(
                'route_brand_edit',
                [
                    'slug' => $brand->slug,
                ]
            );
        }

        if ($form->isSubmitted()) {
            $this->flashMessageRepository->addWarning('Invalid configuration submitted, please correct the errors');
        }

        $moduleFieldNames = [];

        foreach ($form->all() as $formField) {
            if ($formField instanceof SubmitButton || $formField->getName() === Brand::KEY_ACTIVE) {
                continue;
            }

            $moduleFieldNames[$formField->getName()] = $formField->getErrors(true)->count();
        }

        foreach ($brandModuleAdSenseStyleIdReader->getForBrandHavingMultipleModules($brand) as $brandModuleAdSenseStyleId) {
            $this->flashMessageRepository->addNotice(
                sprintf(
                    'AdSense Style ID %s is used in multiple modules (%s)',
                    $brandModuleAdSenseStyleId->styleId,
                    implode(', ', $brandModuleAdSenseStyleId->modules)
                )
            );
        }

        return $this->render(
            'brand/edit.html.twig',
            [
                'brand'              => $brand,
                'form'               => $form,
                'module_field_names' => $moduleFieldNames,
            ]
        );
    }

    #[Route(
        path        : '/brand/{slug:brand}/edit/slug',
        name        : 'route_brand_edit_slug',
        requirements: ['slug' => Brand::SLUG_REGEX],
        methods     : ['GET', 'POST'],
        condition   : "service('route_checker_brand_edit_slug').check(params['slug'])"
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function editBrandSlug(Brand $brand, Request $request): Response
    {
        $form = $this->createForm(BrandSlugType::class, $brand);
        $form->handleRequest($request);

        if ($form->isSubmitted() && $form->isValid()) {
            $this->brandRepository->store($brand);

            if ($brand->isModified()) {
                $this->flashMessageRepository->addSuccess('Brand has been updated');

                return $this->redirectToRoute('route_brand_index');
            }

            $this->flashMessageRepository->addNotice('Brand has no changes');
        }

        return $this->render(
            'brand/edit_slug.html.twig',
            [
                'brand' => $brand,
                'form'  => $form,
            ]
        );
    }

    #[Route(
        path        : '/brand/{slug:brand}/audit-log',
        name        : 'route_brand_audit_log',
        requirements: ['slug' => Brand::SLUG_REGEX],
        methods     : ['GET'],
    )]
    #[IsGranted(SecurityRole::MAINTAINER->value)]
    public function auditLog(Brand $brand, AuditLogRequest $auditLogRequest): Response
    {
        $daysAgo = $auditLogRequest->getDaysAgo() ?? self::DEFAULT_DAYS_AGO;
        $startDate = $this->dateTimeFactory->create(sprintf('today - %u day', $daysAgo), TimezoneEnum::UTC);
        $endDate = $this->dateTimeFactory->create('today', TimezoneEnum::UTC);

        $auditLogs = $this->auditLogRepository->findWithFilters(
            objectId   : $brand->id,
            objectTypes: ['brand'],
            startDate  : $startDate,
            endDate    : $endDate,
            limit      : 200
        );

        return $this->render(
            'brand/audit_log.html.twig',
            [
                'brand'          => $brand,
                'audit_logs'     => $auditLogs,
                'days_ago'       => $daysAgo,
                'action_options' => AuditLogAction::cases(),
            ]
        );
    }
}
