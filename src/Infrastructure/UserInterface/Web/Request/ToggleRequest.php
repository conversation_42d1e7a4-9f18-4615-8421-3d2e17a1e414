<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request;

use Infrastructure\UserInterface\Web\Request\Manager\RequestManagerInterface;

final class ToggleRequest implements RequestInterface
{
    public const string PARAMETER_TOGGLE = 'toggle';

    private bool $toggle;

    public function __construct(
        private readonly RequestManagerInterface $requestManager
    )
    {
    }

    public function isActive(): bool
    {
        if (!isset($this->toggle)) {
            $this->toggle = $this->requestManager->queryBag()->getNullableBool(self::PARAMETER_TOGGLE) ?? true;
        }

        return $this->toggle;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_TOGGLE => $this->isActive(),
        ];
    }
}
