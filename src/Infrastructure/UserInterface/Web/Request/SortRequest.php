<?php

declare(strict_types=1);

namespace Infrastructure\UserInterface\Web\Request;

use Infrastructure\UserInterface\Web\Request\Manager\RequestManagerInterface;
use Infrastructure\UserInterface\Web\Request\Normalizer\RequestPropertyNormalizerInterface;

final class SortRequest implements RequestInterface
{
    public const string PARAMETER_SORT           = 'sort';
    public const string PARAMETER_SORT_DIRECTION = 'sort_direction';

    public const string SORT_DIRECTION_ASC  = 'asc';
    public const string SORT_DIRECTION_DESC = 'desc';

    private const array SORT_DIRECTION_VALUES = [
        self::SORT_DIRECTION_ASC,
        self::SORT_DIRECTION_DESC,
    ];

    private string $sort;

    private ?string $sortDirection;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getSort(): ?string
    {
        if (!isset($this->sort)) {
            $this->sort = $this->requestManager->queryBag()->getString(self::PARAMETER_SORT);
        }

        return $this->requestPropertyNormalizer->getString($this->sort);
    }

    public function getSortDirection(): ?string
    {
        if (!isset($this->sortDirection)) {
            $sortDirection = $this->requestManager->queryBag()->getAcceptedString(
                self::PARAMETER_SORT_DIRECTION,
                self::SORT_DIRECTION_VALUES,
            );

            $this->sortDirection = $sortDirection !== ''
                ? $sortDirection
                : null;
        }

        return $this->sortDirection;
    }

    public function getDirectionForColumn(string $column): string
    {
        return $column === $this->getSort() && $this->getSortDirection() === self::SORT_DIRECTION_DESC
            ? self::SORT_DIRECTION_ASC
            : self::SORT_DIRECTION_DESC;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_SORT           => $this->getSort(),
            self::PARAMETER_SORT_DIRECTION => $this->getSortDirection(),
        ];
    }
}
