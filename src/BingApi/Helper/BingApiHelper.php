<?php

declare(strict_types=1);

namespace App\BingApi\Helper;

use App\Locale\Settings\LocaleSettingsHelperInterface;
use Visymo\BingApiClient\BingApiMarketMapping\BingApiMarketMapping;
use Visymo\BingApiClient\BingApiMarketMapping\Exception\BingApiMarketMappingNotFoundException;

readonly class Bing<PERSON><PERSON>Helper
{
    public function __construct(
        private BingApiMarketMapping $apiMarketMapping,
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    public function isSupported(): bool
    {
        try {
            $this->apiMarketMapping->getMarket($this->localeSettingsHelper->getSettings()->getCode());

            return true;
        } catch (BingApiMarketMappingNotFoundException) {
            return false;
        }
    }
}
