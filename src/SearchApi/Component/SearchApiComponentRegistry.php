<?php

declare(strict_types=1);

namespace App\SearchApi\Component;

use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentRendererLocator;
use App\JsonTemplate\Component\Parent\ParentComponentInterface;
use App\JsonTemplate\Component\Processed\ComponentRendererConditionalSearchApiRequestInterface;
use App\JsonTemplate\Component\Processed\ComponentRendererWithSearchApiDependencyInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\ViewInterface;

final class SearchApiComponentRegistry
{
    /** @var array<string, ParentComponentInterface> */
    private array $parentChildComponentMapping = [];

    /** @var array<string, true> */
    private array $processedComponentIds = [];

    /** @var array<string, ComponentSearchApiDependency> */
    private array $componentsWithSearchApiDependency = [];

    public function __construct(
        private readonly ComponentRendererLocator $componentRendererLocator
    )
    {
    }

    public function addComponentWithSearchApiDependency(
        ComponentInterface $component,
        ViewDataConditionCollection $conditions
    ): void
    {
        $this->componentsWithSearchApiDependency[$component->getId()] = new ComponentSearchApiDependency(
            component : $component,
            conditions: $conditions,
        );
    }

    public function addParentComponentAsChildComponentProcessedHandler(
        ParentComponentInterface $parentComponent
    ): void
    {
        // Validate that the renderer is supported
        $this->getRenderer($parentComponent);

        foreach ($parentComponent->getComponents() as $childComponent) {
            $this->parentChildComponentMapping[$childComponent->getId()] = $parentComponent;
        }
    }

    /**
     * @param ComponentInterface[] $components
     */
    public function handleSearchApiRequestCompleted(array $components, ViewInterface $view): void
    {
        foreach ($components as $component) {
            $this->componentHandleSearchApiCompleted($component, $view);

            $this->processedComponentIds[$component->getId()] = true;
            $parentComponent = $this->parentChildComponentMapping[$component->getId()] ?? null;

            if ($parentComponent === null) {
                continue;
            }

            $this->getRenderer($parentComponent)->handleSearchApiRequestCompleted($parentComponent, $component);
        }
    }

    public function processComponentsWithSearchApiDependency(ViewInterface $view): void
    {
        foreach ($this->componentsWithSearchApiDependency as $searchApiDependency) {
            $this->componentHandleSearchApiCompleted(
                component: $searchApiDependency->component,
                view     : $view,
            );
        }
    }

    private function componentHandleSearchApiCompleted(ComponentInterface $component, ViewInterface $view): void
    {
        $componentRenderer = $this->componentRendererLocator
            ->getRenderer($component);

        if (!$componentRenderer instanceof ComponentRendererWithSearchApiDependencyInterface) {
            return;
        }

        $searchApiDependency = $this->componentsWithSearchApiDependency[$component->getId()] ?? null;

        if ($searchApiDependency !== null && !$searchApiDependency->conditions->check($view->getDataRegistry())) {
            return;
        }

        $componentRenderer->handleSearchApiCompleted($component, $view);
        unset($this->componentsWithSearchApiDependency[$component->getId()]);
    }

    private function getRenderer(
        ParentComponentInterface $parentComponent
    ): ComponentRendererConditionalSearchApiRequestInterface
    {
        $renderer = $this->componentRendererLocator->getRenderer($parentComponent);

        if ($renderer instanceof ComponentRendererConditionalSearchApiRequestInterface) {
            return $renderer;
        }

        throw ComponentRendererIsNotSupportedException::create($renderer);
    }

    /**
     * @return array<string, true>
     */
    public function getProcessedComponentIds(): array
    {
        return $this->processedComponentIds;
    }
}
