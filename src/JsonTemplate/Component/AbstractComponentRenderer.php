<?php

declare(strict_types=1);

namespace App\JsonTemplate\Component;

use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;

abstract class AbstractComponent<PERSON><PERSON><PERSON> implements ComponentRendererInterface
{
    public function build(
        ComponentInterface $component,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void
    {
        $this->buildRequest($component, $view->getDataRequest(), $conditions);
        $this->registerDataRequirements($component, $view->getDataRequest(), $conditions);
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        // can be empty by default
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        // can be empty by default
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        return '';
    }

    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string
    {
        return '';
    }

    public function renderFooters(ComponentInterface $component, ViewInterface $view): string
    {
        return '';
    }
}
