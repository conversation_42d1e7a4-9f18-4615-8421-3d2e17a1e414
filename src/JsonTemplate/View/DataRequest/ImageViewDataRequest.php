<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

class ImageViewDataRequest implements SearchApiViewDataRequestInterface
{
    private bool $enabled = false;

    /**
     * @inheritDoc
     */
    public function mergeWith(array $registries): void
    {
        foreach ($registries as $registry) {
            if (!$registry instanceof self) {
                continue;
            }

            if ($registry->isEnabled()) {
                $this->enable();
            }
        }
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function enable(): self
    {
        $this->enabled = true;

        return $this;
    }

    public function canUseSameSearchApiRequest(SearchApiViewDataRequestInterface $viewDataRequest): bool
    {
        if (!$viewDataRequest instanceof self) {
            return false;
        }

        return true;
    }
}
