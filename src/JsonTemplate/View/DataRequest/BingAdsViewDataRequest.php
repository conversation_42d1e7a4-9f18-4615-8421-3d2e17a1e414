<?php

declare(strict_types=1);

namespace App\JsonTemplate\View\DataRequest;

class BingAdsViewDataRequest implements ViewDataRequestInterface
{
    /** @var BingAdsAdUnitViewDataRequestInterface[] */
    private array $adUnits = [];

    public static function getType(): string
    {
        return 'bing_ads';
    }

    /**
     * @inheritDoc
     */
    public function mergeWith(array $registries): self
    {
        foreach ($registries as $registry) {
            foreach ($registry->getAdUnits() as $adUnit) {
                $this->addAdUnit($adUnit);
            }
        }

        return $this;
    }

    /**
     * @return BingAdsAdUnitViewDataRequestInterface[]
     */
    public function getAdUnits(): array
    {
        return $this->adUnits;
    }

    public function addAdUnit(BingAdsAdUnitViewDataRequestInterface $adUnit): self
    {
        foreach ($this->adUnits as $unit) {
            if ($unit->getContainer() === $adUnit->getContainer()) {
                throw new \InvalidArgumentException(
                    sprintf('Bing Ads ad container "%s" already in use', $adUnit->getContainer()),
                );
            }

            if ($unit->getPosition() === $adUnit->getPosition()) {
                throw new \InvalidArgumentException(
                    sprintf('Bing Ads ad position "%s" already in use', $adUnit->getPosition()),
                );
            }
        }

        $this->adUnits[] = $adUnit;

        return $this;
    }

    public function hasAdUnits(): bool
    {
        return $this->adUnits !== [];
    }
}
