<?php

declare(strict_types=1);

namespace App\JsonTemplate\View;

use App\Brand\Settings\BrandSettingsHelper;
use App\JsonTemplate\Event\JsonTemplateHandledEvent;
use App\JsonTemplate\View\DataRequest\ViewDataRequestBuilder;
use App\Locale\Settings\LocaleSettingsHelperInterface;
use App\SearchApi\SearchApiManager;
use Symfony\Component\EventDispatcher\EventDispatcherInterface;
use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Response;
use Twig\Environment;

final readonly class JsonTemplateViewHandler
{
    public function __construct(
        private ViewDataRequestBuilder $viewDataRequestBuilder,
        private SearchApiManager $searchApiManager,
        private JsonTemplateViewRegistry $jsonTemplateViewRegistry,
        private EventDispatcherInterface $eventDispatcher,
        private Environment $twig,
        private BrandSettingsHelper $brandSettingsHelper,
        private LocaleSettingsHelperInterface $localeSettingsHelper
    )
    {
    }

    public function handle(ViewInterface $view): Response
    {
        $viewDataRequest = $view->getDataRequest();

        $this->viewDataRequestBuilder->buildForView($view);

        $this->searchApiManager->search($view);

        // Finalize requests after SearchAPI response
        $viewDataRequest->finalize($view->getDataRegistry());

        // This will only hold the last handled view.
        // Multiple handled views only occur when the error controller is called.
        $this->jsonTemplateViewRegistry->setView($view);

        // Check the requirements for the view data, after building the request and
        // the Composite Search API response and before rendering the view.
        $requirements = $view->getDataRequest()->getRequirements();
        $view->getDataRegistry()->requireForView($requirements);

        $this->eventDispatcher->dispatch(
            new JsonTemplateHandledEvent($view),
            JsonTemplateHandledEvent::NAME,
        );

        return $this->render($view);
    }

    private function render(ViewInterface $view): Response
    {
        $content = $this->twig->render(
            $view->getTwigTemplate(),
            [
                'view'      => $view,
                'container' => $view->getContainer(),
                'locale'    => $this->localeSettingsHelper->getSettings(),
                'brand'     => $this->brandSettingsHelper->getSettings(),
            ],
        );

        if ($view->getDataRegistry()->redirectUrl !== null) {
            return new RedirectResponse(
                url    : $view->getDataRegistry()->redirectUrl,
                headers: $view->getResponse()->headers->all(),
            );
        }

        return $view->getResponse()->setContent($content);
    }
}
