<?php

declare(strict_types=1);

namespace Domain\Scraper;

use Domain\Scraper\ScraperBatch\ScraperBatchStatus;

interface ScraperClientInterface
{
    /**
     * @param array<int, string> $urls
     */
    public function scheduleRun(array $urls): string;

    public function getRunStatus(string $runId): ScraperBatchStatus;

    public function getName(): string;

    public function abortRun(string $runId): void;
}
