<?php

declare(strict_types=1);

namespace Domain\AdSenseStyleId;

use Domain\AdSenseStyleId\Exception\InvalidAdSenseStyleIdException;

final readonly class AdSenseStyleId implements \JsonSerializable
{
    public const string PATTERN = '/^[1-9]\d{9}$/';

    private function __construct(
        private string $styleId
    )
    {
        if (!$this->isValid($styleId)) {
            throw InvalidAdSenseStyleIdException::forStyleId($styleId);
        }
    }

    public static function fromStyleId(string $styleId): self
    {
        return new self($styleId);
    }

    public function equals(self $adSenseStyleId): bool
    {
        return $this->styleId === $adSenseStyleId->styleId;
    }

    public function __toString(): string
    {
        return $this->styleId;
    }

    public function jsonSerialize(): string
    {
        return $this->styleId;
    }

    private function isValid(string $styleId): bool
    {
        return preg_match(self::PATTERN, $styleId) === 1;
    }
}
