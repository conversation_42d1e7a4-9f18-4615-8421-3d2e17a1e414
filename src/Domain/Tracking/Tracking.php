<?php

declare(strict_types=1);

namespace Domain\Tracking;

use Domain\Brand\Brand;
use Domain\BrandModule\AbstractBrandModule;

class Tracking extends AbstractBrandModule
{
    public Brand $brand;

    public function __construct(
        public bool $campaignNameValidationEnabled
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'tracking';
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): void
    {
        $this->brand = $brand;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            'campaign_name_validation_enabled' => $this->campaignNameValidationEnabled,
        ];
    }
}
