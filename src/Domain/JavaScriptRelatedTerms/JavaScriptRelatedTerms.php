<?php

declare(strict_types=1);

namespace Domain\JavaScriptRelatedTerms;

use Domain\AdSenseStyleId\AdSenseStyleId;
use Domain\Brand\Brand;
use Domain\BrandModule\AbstractBrandModule;
use Domain\BrandModule\Enabled\BrandModuleEnabledInterface;
use Domain\BrandModule\SearchRoute\BrandModuleSearchRouteDependencyInterface;
use Domain\SearchRoute\SearchRoute;

class JavaScriptRelatedTerms extends AbstractBrandModule implements
    BrandModuleEnabledInterface,
    BrandModuleSearchRouteDependencyInterface
{
    public Brand $brand;

    public function __construct(
        public bool $enabled,
        public ?AdSenseStyleId $defaultStyleId,
        public bool $contentEnabled,
        public ?SearchRoute $contentClickRoute,
        public bool $searchEnabled,
        public ?SearchRoute $searchClickRoute
    )
    {
    }

    public static function getModuleName(): string
    {
        return 'javascript_related_terms';
    }

    public function isEnabled(): bool
    {
        return $this->enabled;
    }

    public function getBrand(): Brand
    {
        return $this->brand;
    }

    public function setBrand(Brand $brand): void
    {
        $this->brand = $brand;
    }

    /**
     * @inheritDoc
     */
    public function getSearchRouteDependencies(): array
    {
        if (!$this->enabled) {
            return [];
        }

        $searchRoutes = [];

        if ($this->contentEnabled && $this->contentClickRoute !== null) {
            $searchRoutes[] = $this->contentClickRoute;
        }

        if ($this->searchEnabled && $this->searchClickRoute !== null) {
            $searchRoutes[] = $this->searchClickRoute;
        }

        return $searchRoutes;
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        $data = [
            self::KEY_ENABLED => $this->enabled,
        ];

        if ($this->enabled) {
            $data['default_style_id'] = $this->defaultStyleId;
            $data['content_enabled'] = $this->contentEnabled;
            $data['content_click_route'] = $this->contentClickRoute?->value;
            $data['search_enabled'] = $this->searchEnabled;
            $data['search_click_route'] = $this->searchClickRoute?->value;
        }

        return $data;
    }
}
