<?php

declare(strict_types=1);

namespace App\GoogleCsa\Generator;

use App\Debug\Request\DebugRequestInterface;
use App\SplitTest\SplitTestExtendedReaderInterface;

final class GoogleRelatedTermsContainerGenerator implements GoogleRelatedTermsContainerGeneratorInterface
{
    private static int $containerCount = 0;

    public function __construct(
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly DebugRequestInterface $debugRequest
    )
    {
    }

    public function generateContainer(?string $suffix): string
    {
        self::$containerCount++;
        $containerWithSuffix = $this->getContainerWithSuffix($suffix);
        $containerPrefix = $this->getContainerPrefix();

        return $containerPrefix !== null
            ? sprintf('%s-%s', $containerPrefix, $containerWithSuffix)
            : $containerWithSuffix;
    }

    private function getContainerWithSuffix(?string $suffix = null): string
    {
        $splitTestContainerSuffix = $this->splitTestExtendedReader->getContainerSuffix();
        $debugSuffix = $this->debugRequest->getDebugForceCsaContainerSuffix();
        $additionalContainerSuffix = $debugSuffix ?? $splitTestContainerSuffix;

        if ($suffix === null && $this->splitTestExtendedReader->isVariantActive('krdd')) {
            $suffix = 'krdd';
        }

        $container = $suffix !== null
            ? sprintf(self::CONTAINER_SUFFIXED_TEMPLATE, $suffix, self::$containerCount)
            : sprintf(self::CONTAINER_TEMPLATE, self::$containerCount);

        if ($additionalContainerSuffix !== null) {
            $container = sprintf('%s-%s', $container, $additionalContainerSuffix);
        }

        return $container;
    }

    private function getContainerPrefix(): ?string
    {
        return $this->debugRequest->getDebugForceCsaContainerPrefix();
    }
}
