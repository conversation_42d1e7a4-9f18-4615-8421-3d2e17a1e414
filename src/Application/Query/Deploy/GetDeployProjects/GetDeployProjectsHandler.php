<?php

declare(strict_types=1);

namespace Application\Query\Deploy\GetDeployProjects;

use Application\Query\Deploy\GetDeployProjects\Model\BuildResult;
use Application\Query\Deploy\GetDeployProjects\Model\ProjectResult;
use Domain\DeployProject\DeployProject;
use Domain\DeployProject\DeployProjectRepositoryInterface;
use Domain\GitLabApi\Commit\CommitRepositoryInterface;
use Domain\GitLabApi\Pipeline\PipelineRepositoryInterface;
use Domain\GitLabApi\ProjectToken\ProjectTokenCollection;
use Visymo\QueryBus\QueryBus\QueryHandlerInterface;

final readonly class GetDeployProjectsHandler implements QueryHandlerInterface
{
    public function __construct(
        private DeployProjectRepositoryInterface $deployProjectRepository,
        private CommitRepositoryInterface $commitRepository,
        private PipelineRepositoryInterface $pipelineRepository,
        private ProjectTokenCollection $projectTokenCollection
    )
    {
    }

    public function handle(GetDeployProjectsQuery $query): GetDeployProjectsResponse
    {
        $results = [];

        foreach ($this->deployProjectRepository->findAll() as $deployProject) {
            if (!$this->projectTokenCollection->hasProjectToken($deployProject->gitLabProjectId)) {
                continue;
            }

            $masterCommit = $this->commitRepository->findLatestMasterCommit(
                projectId: $deployProject->gitLabProjectId
            );

            // Collect server builds
            $availableBuilds = [];
            $activeBuilds = [];

            foreach ($deployProject->builds as $deployBuild) {
                $availableBuilds[$deployBuild->build] = [];
            }

            foreach ($deployProject->servers as $deployServer) {
                foreach ($deployServer->availableBuilds as $deployBuild) {
                    $availableBuilds[$deployBuild->build][$deployServer->name] = true;
                }

                foreach ($availableBuilds as $build => $servers) {
                    if (!isset($servers[$deployServer->name])) {
                        $availableBuilds[$build][$deployServer->name] = false;
                    }
                }

                if ($deployServer->activeBuild !== null) {
                    $activeBuilds[$deployServer->activeBuild->build] = true;
                }
            }

            $buildResults = [];

            foreach ($deployProject->builds as $deployBuild) {
                $availableOnServers = array_keys(
                    array_filter($availableBuilds[$deployBuild->build], static fn (bool $available) => $available)
                );
                $notAvailableOnServers = array_keys(
                    array_filter($availableBuilds[$deployBuild->build], static fn (bool $available) => !$available)
                );
                $active = isset($activeBuilds[$deployBuild->build]);
                $buildVersion = $deployBuild->getBuildVersion();

                $buildResults[$buildVersion] = new BuildResult(
                    build                   : $deployBuild->build,
                    buildVersion            : $buildVersion,
                    active                  : $active,
                    valid                   : !$deployBuild->invalid,
                    equalsLatestMasterCommit: $deployBuild->commitSha === $masterCommit->id,
                    availableOnServers      : $availableOnServers,
                    notAvailableOnServers   : $notAvailableOnServers
                );
            }

            // Sort on build version
            $buildVersions = array_keys($buildResults);
            array_multisort($buildVersions, SORT_NATURAL, $buildResults);

            if (!$deployProject->allowDeploy()) {
                $deploymentInProgress = true;
            } else {
                $deploymentInProgress = $this->hasDeploymentPipelineInProgress($deployProject);
            }

            $results[] = new ProjectResult(
                name                : $deployProject->name,
                gitLabProjectId     : $deployProject->gitLabProjectId,
                buildResults        : array_values($buildResults),
                deploymentInProgress: $deploymentInProgress
            );
        }

        return new GetDeployProjectsResponse($results);
    }

    private function hasDeploymentPipelineInProgress(DeployProject $deployProject): bool
    {
        $pipelines = $this->pipelineRepository->findAll(
            projectId: $deployProject->gitLabProjectId,
            name     : 'Production deployment',
            page     : 1,
            limit    : 1,
            orderBy  : 'id',
            sort     : 'desc'
        );

        foreach ($pipelines as $pipeline) {
            return $pipeline->status->inProgressState();
        }

        return false;
    }
}
