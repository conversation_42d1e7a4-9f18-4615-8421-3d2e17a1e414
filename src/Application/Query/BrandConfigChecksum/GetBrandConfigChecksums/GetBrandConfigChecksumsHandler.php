<?php

declare(strict_types=1);

namespace Application\Query\BrandConfigChecksum\GetBrandConfigChecksums;

use Domain\AntelopeBrand\AntelopeBrandRepositoryInterface;
use Domain\Brand\BrandRepositoryInterface;
use Domain\BrandWebsite\Client\BrandWebsiteClientInterface;
use Psr\Log\LoggerInterface;
use Visymo\QueryBus\QueryBus\QueryHandlerInterface;

final readonly class GetBrandConfigChecksumsHandler implements QueryHandlerInterface
{
    public function __construct(
        private BrandRepositoryInterface $brandRepository,
        private AntelopeBrandRepositoryInterface $antelopeBrandRepository,
        private BrandWebsiteClientInterface $brandWebsiteClient,
        private LoggerInterface $logger
    )
    {
    }

    public function handle(GetBrandConfigChecksumsQuery $query): GetBrandConfigChecksumsResponse
    {
        return new GetBrandConfigChecksumsResponse(
            $this->fetchAllBrandConfigChecksums()
        );
    }

    /**
     * @return array<string, string>
     */
    private function fetchAllBrandConfigChecksums(): array
    {
        $domain = $this->getDomain();

        if ($domain === null) {
            $this->logger->error('No domain found to fetch brand config checksums');

            return [];
        }

        try {
            return $this->brandWebsiteClient->getProjectBrandsConfigChecksum($domain);
        } catch (\Throwable $exception) {
            $this->logger->error(
                'Failed to fetch brand config checksums: {message}',
                [
                    'exception' => $exception,
                    'message'   => $exception->getMessage(),
                    'domain'    => $domain,
                ]
            );

            return [];
        }
    }

    private function getDomain(): ?string
    {
        $brand = $this->brandRepository->findOneActiveWithoutPartnerSlug();

        if ($brand === null) {
            return null;
        }

        return $this->antelopeBrandRepository->findOneBySlug($brand->slug)
            ->getFirstDomain();
    }
}
