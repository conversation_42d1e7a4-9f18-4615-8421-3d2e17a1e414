<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageParagraphs;

use App\ContentPage\Paragraph\ParagraphsAmountRegistry;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\ComponentRendererLocator;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\KeywordHighlightOption;
use App\SearchApi\SearchApiManager;
use Twig\Environment;

final class ContentPageParagraphsRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly ParagraphsAmountRegistry $paragraphsAmountRegistry,
        private readonly ComponentRendererLocator $componentRendererLocator,
        private readonly SearchApiManager $searchApiManager
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE,
                ViewDataProperty::QUERY,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageParagraphsComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageParagraphsComponent::class]);
        }

        $contentPageViewDataRequest = $request->contentPage()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
            searchApiViewDataRequest: $contentPageViewDataRequest,
            conditions              : $conditions,
        );

        $this->paragraphsAmountRegistry->registerParagraphComponent($component);
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageParagraphsComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageParagraphsComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $contentPage = $viewDataRegistry->getContentPage()->page;

        if ($contentPage === null) {
            return '';
        }

        $amount = $component->amount ?? count($contentPage->paragraphs);

        if ($component->layout === ContentPageParagraphsLayout::CONTAINER) {
            return $this->renderSingleParagraphs($amount, $component, $view);
        }

        $paragraphs = array_slice(
            $contentPage->paragraphs,
            $this->paragraphsAmountRegistry->getAmountOfResultsRegistered(),
            $amount,
        );
        $this->paragraphsAmountRegistry->increaseAmountOfResultsRegistered($amount);

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'paragraphs'                => $paragraphs,
                'query'                     => (string)$viewDataRegistry->getQuery(),
                'layout'                    => $component->layout->value,
                'keyword_highlight_option'  => $viewDataRegistry->getKeywordHighlight(
                    KeywordHighlightOption::DEFAULT_FALSE,
                ),
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }

    private function renderSingleParagraphs(
        int $amount,
        ContentPageParagraphsComponent $component,
        ViewInterface $view
    ): string
    {
        if ($component->paragraphComponent === null) {
            return '';
        }

        $content = '';
        $contentPageParagraphRenderer = $this->componentRendererLocator->getRenderer(
            $component->paragraphComponent,
        );

        for ($i = 0; $i < $amount; $i++) {
            $content .= $contentPageParagraphRenderer->render(
                $component->paragraphComponent,
                $view,
            );
        }

        return $content;
    }
}
