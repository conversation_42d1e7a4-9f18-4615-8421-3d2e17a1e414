<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageFooter;

use App\Brand\Settings\BrandSettingsHelper;
use App\BrandAssets\Exception\BrandAssetsImageNotFoundException;
use App\BrandAssets\File\BrandAssetsImageFileName;
use App\BrandAssets\Settings\BrandAssetsSettings;
use App\Component\Content\ContentPageFooter\Model\AuthorItem;
use App\ContentPage\Author\Author;
use App\ContentPage\Author\AuthorFactory;
use App\ContentPage\Settings\ContentPageSettings;
use App\Debug\Request\DebugRequestInterface;
use App\Generic\Date\PublishedDateHelper;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use App\SharedStyle\SharedStyleRegistry;
use Twig\Environment;

class ContentPageFooterRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly PublishedDateHelper $publishedDateHelper,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly BrandAssetsSettings $brandAssetsSettings,
        private readonly SharedStyleRegistry $sharedStyleRegistry,
        private readonly SearchApiManager $searchApiManager,
        private readonly ContentPageSettings $contentPageSettings,
        private readonly AuthorFactory $authorFactory,
        private readonly DebugRequestInterface $debugRequest
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageFooterComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageFooterComponent::class]);
        }

        $contentPageViewDataRequest = $request->contentPage()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
            searchApiViewDataRequest: $contentPageViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageFooterComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageFooterComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();
        $contentPage = $viewDataRegistry->getContentPage()->page;

        if ($contentPage === null) {
            return '';
        }

        $publishedDate = $this->publishedDateHelper->getPublishedDate(
            publishedDate: $contentPage->publishedAt,
            query        : (string)$viewDataRegistry->getQuery(),
        );

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'brand_name'                => $this->brandSettingsHelper->getSettings()->getName(),
                'reading_time'              => $contentPage->readingTime,
                'published_date'            => $publishedDate,
                'author_item'               => $this->getAuthorItem(),
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }

    private function getAuthorItem(): AuthorItem
    {
        if ($this->contentPageSettings->author->slug !== Author::EDITORIAL_TEAM_SLUG
            && !$this->debugRequest->showFixedStats()
        ) {
            $authorImage = $this->getAuthorImage();

            if ($authorImage !== null) {
                return new AuthorItem(
                    author: $this->contentPageSettings->author,
                    image : $authorImage,
                );
            }
        }

        return new AuthorItem(
            author: $this->authorFactory->createEditorialTeam(),
            image : $this->sharedStyleRegistry->getSharedStyle()->getAsset('/images/team/editorial-small.jpg'),
        );
    }

    /**
     * @return string|null Base64 image
     */
    private function getAuthorImage(): ?string
    {
        try {
            return $this->brandAssetsSettings->getImage(BrandAssetsImageFileName::AUTHOR_JPG);
        } catch (BrandAssetsImageNotFoundException) {
            return null;
        }
    }
}
