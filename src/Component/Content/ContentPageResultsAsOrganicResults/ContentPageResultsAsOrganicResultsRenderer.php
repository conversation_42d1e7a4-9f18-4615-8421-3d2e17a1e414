<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageResultsAsOrganicResults;

use App\Component\Content\ContentPageResultsAsOrganicResults\Result\ContentPageToOrganicResultFactory;
use App\Component\Content\OrganicContentPageResults\OrganicContentPageResultsSupport;
use App\Component\Generic\Results\ResultsAmountOptimizer;
use App\Component\Generic\Results\ResultsAmountRegistry;
use App\Component\Generic\Results\ResultsType;
use App\Component\Web\OrganicResults\OrganicResultsRenderer;
use App\ContentPage\Request\ContentPageRequestInterface;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\Processed\ComponentRendererWithSearchApiDependencyInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResult;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPage;

final class ContentPageResultsAsOrganicResultsRenderer extends AbstractComponentRenderer
    implements ComponentRendererWithSearchApiDependencyInterface
{
    private const int MAX_DESCRIPTION_LENGTH = 160;

    public function __construct(
        private readonly ContentPageRequestInterface $contentPageRequest,
        private readonly ContentPageToOrganicResultFactory $contentPageToOrganicResultFactory,
        private readonly OrganicContentPageResultsSupport $organicContentPageResultsSupport,
        private readonly ResultsAmountRegistry $resultsAmountRegistry,
        private readonly OrganicResultsRenderer $organicResultsRenderer,
        private readonly SearchApiManager $searchApiManager,
        private readonly ResultsAmountOptimizer $resultsAmountOptimizer
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
                ViewDataProperty::CONTENT_PAGES,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageResultsAsOrganicResultsComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageResultsAsOrganicResultsComponent::class]);
        }

        if (!$this->organicContentPageResultsSupport->isSupported($component)) {
            return;
        }

        $contentPagesViewDataRequest = $request->contentPages();
        $contentPagesViewDataRequest
            ->enable()
            ->increasePageSize($component->getAmount())
            ->excludeParagraphs();

        if ($component->linkToActiveBrand !== null) {
            $contentPagesViewDataRequest->setMultipleCollectionsSupported($component->linkToActiveBrand === false);
        }

        if ($this->contentPageRequest->getPreviousPublicId() !== null) {
            $contentPagesViewDataRequest->setRelevantPublicId(
                $this->contentPageRequest->getPreviousPublicId(),
            );
        }

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGES,
            searchApiViewDataRequest: $contentPagesViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function handleSearchApiCompleted(
        ComponentInterface $component,
        ViewInterface $view
    ): void
    {
        if (!$component instanceof ContentPageResultsAsOrganicResultsComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageResultsAsOrganicResultsComponent::class]);
        }

        if (!$this->organicContentPageResultsSupport->isSupported($component)) {
            return;
        }

        $this->resultsAmountOptimizer->optimizeAmountOfResults($component);

        $contentPagesResponse = $view->getDataRegistry()->getContentPages($component);

        $this->resultsAmountRegistry->registerResults(
            component  : $component,
            resultsType: ResultsType::ORGANIC,
            resultsKey : spl_object_hash($contentPagesResponse),
            results    : $contentPagesResponse->getResults(),
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageResultsAsOrganicResultsComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageResultsAsOrganicResultsComponent::class]);
        }

        if (!$this->organicContentPageResultsSupport->isSupported($component)) {
            return '';
        }

        $organicResults = $this->getOrganicResults($component);

        return $this->organicResultsRenderer->renderOrganicResults($component, $view, $organicResults);
    }

    /**
     * @return OrganicResult[]
     */
    private function getOrganicResults(
        ContentPageResultsAsOrganicResultsComponent $component
    ): array
    {
        /** @var ContentPage[] $contentPages */
        $contentPages = $this->resultsAmountRegistry->getResultsByComponent(
            component: $component,
        );
        $maxDescriptionLength = $component->maxDescriptionLength ?? self::MAX_DESCRIPTION_LENGTH;
        $organicResults = [];

        foreach ($contentPages as $contentPage) {
            $organicResult = $this->contentPageToOrganicResultFactory->create(
                $contentPage,
                $maxDescriptionLength,
                $component->linkToActiveBrand,
            );

            if ($organicResult === null) {
                break;
            }

            $organicResults[] = $organicResult;
        }

        return $organicResults;
    }
}
