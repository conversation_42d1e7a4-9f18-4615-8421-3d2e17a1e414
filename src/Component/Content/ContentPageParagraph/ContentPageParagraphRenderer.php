<?php

declare(strict_types=1);

namespace App\Component\Content\ContentPageParagraph;

use App\ContentPage\Paragraph\ParagraphsAmountRegistry;
use App\ContentPage\Paragraph\ParagraphSlugRegistry;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\KeywordHighlightOption;
use App\SearchApi\SearchApiManager;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageParagraph;
use Visymo\Shared\Domain\Formatter\TruncateFormatterFactory;

class ContentPageParagraphRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly ParagraphsAmountRegistry $paragraphsAmountRegistry,
        private readonly TruncateFormatterFactory $truncateFormatterFactory,
        private readonly SearchApiManager $searchApiManager,
        private readonly ParagraphSlugRegistry $paragraphSlugRegistry
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::CONTENT_PAGE,
                ViewDataProperty::QUERY,
            ],
        );
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof ContentPageParagraphComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageParagraphComponent::class]);
        }

        $contentPageViewDataRequest = $request->contentPage()->enable();

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE,
            searchApiViewDataRequest: $contentPageViewDataRequest,
            conditions              : $conditions,
        );

        $this->paragraphsAmountRegistry->registerParagraphComponent($component);
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ContentPageParagraphComponent) {
            throw UnsupportedComponentException::create($component, [ContentPageParagraphComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $paragraph = $this->getParagraph($component, $viewDataRegistry);

        if ($paragraph === null) {
            return '';
        }

        $description = $this->getDescription($component, $paragraph);

        if ($description === null) {
            return '';
        }

        if ($component->isStartOfParagraph()) {
            $title = $paragraph->title;
            $this->paragraphsAmountRegistry->increaseAmountOfResultsRegistered(1);
        } else {
            $title = null;
        }

        $titleSlug = $title !== null
            ? $this->paragraphSlugRegistry->getSlug($title)
            : null;

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'query'                     => (string)$viewDataRegistry->getQuery(),
                'title'                     => $title,
                'title_slug'                => $titleSlug,
                'description'               => $description,
                'layout'                    => $component->layout->value,
                'keyword_highlight_option'  => $viewDataRegistry->getKeywordHighlight(KeywordHighlightOption::DEFAULT_FALSE),
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }

    private function getParagraph(
        ContentPageParagraphComponent $component,
        ViewDataRegistry $viewDataRegistry
    ): ?ContentPageParagraph
    {
        $contentPage = $viewDataRegistry->getContentPage()->page;

        if ($contentPage === null) {
            return null;
        }

        if (!$component->isStartOfParagraph()) {
            // Use previous or first paragraph
            $sliceOffset = max($this->paragraphsAmountRegistry->getAmountOfResultsRegistered() - 1, 0);
        } else {
            $sliceOffset = $this->paragraphsAmountRegistry->getAmountOfResultsRegistered();
        }

        $paragraphs = array_slice(
            $contentPage->paragraphs,
            $sliceOffset,
            1,
        );

        if ($paragraphs === []) {
            return null;
        }

        return current($paragraphs);
    }

    private function getDescription(ContentPageParagraphComponent $component, ContentPageParagraph $paragraph): ?string
    {
        $truncateFormatter = $this->truncateFormatterFactory->create(
            maxLength       : $component->maxLength,
            startAfterLength: $component->startAfterLength,
            onLineEnd       : $component->splitOnLineEnd,
        );

        $description = $truncateFormatter->format($paragraph->content);

        return $description !== ''
            ? $description
            : null;
    }
}
