<?php

declare(strict_types=1);

namespace App\Component\Image\ImageErrorMessage;

use App\Component\Generic\ContextErrorMessage\ContextErrorMessageComponent;
use App\Component\Generic\ContextErrorMessage\ContextErrorMessageLayout;
use App\Component\Generic\ContextErrorMessage\ContextErrorMessageRenderer;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Image\Response\ImageResponseContext;

class ImageErrorMessageRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly ContextErrorMessageRenderer $contextErrorMessageRenderer
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::SEARCH_RESPONSES,
                ViewDataProperty::IMAGE_RESULTS,
            ],
            $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ImageErrorMessageComponent) {
            throw UnsupportedComponentException::create($component, [ImageErrorMessageComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $searchResponse = $viewDataRegistry->getSearchResponses();
        /** @var ImageResponseContext|null $imageResults */
        $imageResults = $searchResponse->getSearchResponseContext(ViewDataProperty::IMAGE_RESULTS);

        $hasError = $searchResponse->hasFatalError() || (bool)$imageResults?->hasErrors();

        return $this->contextErrorMessageRenderer->render(
            new ContextErrorMessageComponent(
                layout    : ContextErrorMessageLayout::DEFAULT,
                hasResults: (bool)$imageResults?->hasResults(),
                hasError  : $hasError,
            ),
            $view,
        );
    }
}
