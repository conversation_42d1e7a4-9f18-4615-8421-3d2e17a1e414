<?php

declare(strict_types=1);

namespace App\Component\Image\ImageFilters;

use App\Component\Generic\SearchFilters\SearchFiltersFactory;
use App\Component\Generic\SearchFilters\SearchFiltersLayout;
use App\Component\Generic\SearchFilters\SearchFiltersRenderer;
use App\ImageSearch\Filter\ImageColorFilter;
use App\ImageSearch\Filter\ImagePeriodFilter;
use App\ImageSearch\Filter\ImageSizeFilter;
use App\ImageSearch\Filter\ImageTypeFilter;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;

class ImageFiltersRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly ImagePeriodFilter $imagePeriodFilter,
        private readonly ImageColorFilter $imageColorFilter,
        private readonly ImageTypeFilter $imageTypeFilter,
        private readonly ImageSizeFilter $imageSizeFilter,
        private readonly SearchFiltersRenderer $searchFiltersRenderer,
        private readonly SearchFiltersFactory $searchFiltersFactory
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::IMAGE_RESULTS,
            ],
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof ImageFiltersComponent) {
            throw UnsupportedComponentException::create($component, [ImageFiltersComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        if (!$viewDataRegistry->getSearchResponses()->hasResults(ViewDataProperty::IMAGE_RESULTS)) {
            return '';
        }

        return $this->searchFiltersRenderer->render(
            $this->searchFiltersFactory->createWithLayoutAndFilters(
                layout : SearchFiltersLayout::DEFAULT,
                filters: [
                             $this->imagePeriodFilter->getSearchFilter(),
                             $this->imageColorFilter->getSearchFilter(),
                             $this->imageTypeFilter->getSearchFilter(),
                             $this->imageSizeFilter->getSearchFilter(),
                         ],
            ),
            $view,
        );
    }
}
