<?php

declare(strict_types=1);

namespace App\Component\Terms\RelatedTerms;

use App\ConversionTracking\Endpoint\VisymoRelatedTerms\VisymoRelatedTermsConversionUrlGenerator;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\Data\ViewDataRegistry;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Preferences\Option\KeywordHighlightOption;
use App\Search\Registry\RouteRegistry;
use App\SearchApi\SearchApiManager;
use Symfony\Contracts\Translation\TranslatorInterface;
use Twig\Environment;

final class RelatedTermsRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly TranslatorInterface $translator,
        private readonly VisymoRelatedTermsConversionUrlGenerator $visymoRelatedTermsConversionUrlGenerator,
        private readonly SearchApiManager $searchApiManager,
        private readonly RouteRegistry $routeRegistry,
        private readonly RelatedTermsProvider $relatedTermsProvider
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        if (!$component instanceof RelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [RelatedTermsComponent::class]);
        }

        $request->setRequirements([ViewDataProperty::QUERY]);

        if ($this->relatedTermsProvider->requiresSearchResponse()) {
            $request->setRequirements([ViewDataProperty::RELATED_TERMS]);
        }
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof RelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [RelatedTermsComponent::class]);
        }

        if (!$this->relatedTermsProvider->requiresSearchResponse()) {
            return;
        }

        $relatedTermsViewDataRequest = $request->relatedTerms();

        if ($component->repeatTerms) {
            $relatedTermsViewDataRequest->setAmountIfMax($component->amount);
        } else {
            $relatedTermsViewDataRequest->increaseAmount($component->amount);
        }

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::RELATED_TERMS,
            searchApiViewDataRequest: $relatedTermsViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof RelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [RelatedTermsComponent::class]);
        }

        $relatedTerms = $this->relatedTermsProvider->getRelatedTerms($component, $view);

        if ($relatedTerms === []) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'terms'                     => $relatedTerms,
                'query'                     => (string)$view->getDataRegistry()->getQuery(),
                'title'                     => $this->getTitle($component, $view->getDataRegistry()),
                'keyword_highlight_option'  => $this->getKeywordHighlight($component, $view->getDataRegistry()),
                'zone'                      => (string)$component->zone,
                'route'                     => $component->route ?? $this->routeRegistry->getSearchRoute(),
                'columns'                   => $component->columns,
                'layout'                    => $component->layout->value,
                'fallback_component_class'  => $component->getFallbackComponentClass(),
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }

    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof RelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [RelatedTermsComponent::class]);
        }

        return $this->twig->render(
            '@component/Terms/RelatedTerms/layout/default/related-terms-script.html.twig',
            [
                'conversion_url' => $this->visymoRelatedTermsConversionUrlGenerator->generate(),
            ],
        );
    }

    private function getKeywordHighlight(
        RelatedTermsComponent $component,
        ViewDataRegistry $viewDataRegistry
    ): ?string
    {
        return match ($component->keywordHighlight) {
            true  => KeywordHighlightOption::FORCED_TRUE,
            false => KeywordHighlightOption::FORCED_FALSE,
            null  => $viewDataRegistry->getKeywordHighlight(KeywordHighlightOption::DEFAULT_TRUE),
        };
    }

    private function getTitle(RelatedTermsComponent $component, ViewDataRegistry $viewDataRegistry): ?string
    {
        if (!$component->showTitle) {
            return null;
        }

        if ($component->layout === RelatedTermsLayout::COMPACT ||
            $component->layout === RelatedTermsLayout::COMPACT_BLUE ||
            $component->layout === RelatedTermsLayout::COMPACT_LIGHT ||
            $component->layout === RelatedTermsLayout::COMPACT_ZAPMETA
        ) {
            return $this->translator->trans('related_terms.short_title');
        }

        $queryTitleCase = mb_convert_case((string)$viewDataRegistry->getQuery(), MB_CASE_TITLE, 'UTF-8');

        return $this->translator->trans(
            'related_terms.title',
            [
                '%query%' => sprintf('<strong>%s</strong>', htmlspecialchars($queryTitleCase, ENT_COMPAT)),
            ],
        );
    }
}
