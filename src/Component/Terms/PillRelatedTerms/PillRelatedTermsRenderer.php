<?php

declare(strict_types=1);

namespace App\Component\Terms\PillRelatedTerms;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\SearchApi\SearchApiManager;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\RelatedTerms\Response\RelatedTerm;
use Visymo\PrototypeBundle\PillRelatedTerms\Factory\PillRelatedTermCollectionFactory;
use Visymo\PrototypeBundle\PillRelatedTerms\Registry\ActivePillRelatedTermCollectionRegistry;

final class PillRelatedTermsRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly SearchApiManager $searchApiManager,
        private readonly PillRelatedTermCollectionFactory $pillRelatedTermCollectionFactory,
        private readonly ActivePillRelatedTermCollectionRegistry $activePillRelatedTermCollectionRegistry
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
                ViewDataProperty::RELATED_TERMS,
            ],
            $conditions,
        );
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof PillRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [PillRelatedTermsComponent::class]);
        }

        $activeTerms = $this->activePillRelatedTermCollectionRegistry->getActivePillRelatedTermCollection();
        $relatedTermsViewDataRequest = $request->relatedTerms()
            ->setForPills()
            ->setAmountIfMax(max(0, $component->amount - $activeTerms->getAmount()));

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::RELATED_TERMS,
            searchApiViewDataRequest: $relatedTermsViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof PillRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [PillRelatedTermsComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        $relatedTerms = array_map(
            static fn (RelatedTerm $relatedTerm) => $relatedTerm->getQuery(),
            $viewDataRegistry->getRelatedTerms($component)->getResults(),
        );

        $activeTerms = $this->activePillRelatedTermCollectionRegistry->getActivePillRelatedTermCollection();
        $inactiveTerms = $this->pillRelatedTermCollectionFactory->createForInactive($relatedTerms);

        if ($activeTerms->getAmount() === 0 && $inactiveTerms->getAmount() === 0) {
            return '';
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'query'                     => $viewDataRegistry->getQuery(),
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
                'active_terms'              => $activeTerms->pillRelatedTerms,
                'inactive_terms'            => $inactiveTerms->pillRelatedTerms,
            ],
        );
    }
}
