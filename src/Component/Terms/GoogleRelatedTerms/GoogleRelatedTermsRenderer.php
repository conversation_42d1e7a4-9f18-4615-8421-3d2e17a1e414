<?php

declare(strict_types=1);

namespace App\Component\Terms\GoogleRelatedTerms;

use App\Component\Terms\RelatedTerms\RelatedTermsComponent;
use App\Component\Terms\RelatedTerms\RelatedTermsRenderer;
use App\DisplaySearchRelated\Settings\DisplaySearchRelatedSettings;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\Component\Processed\ComponentRendererWithSearchApiDependencyInterface;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\GoogleCsaRelatedSearchUnitViewDataRequest;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Monetization\Settings\MonetizationSettings;
use App\SearchApi\Component\SearchApiComponentRegistry;
use App\SearchApi\SearchApiManager;
use App\SplitTest\SplitTestExtendedReaderInterface;

final class GoogleRelatedTermsRenderer extends AbstractComponentRenderer
    implements ComponentRendererWithSearchApiDependencyInterface
{
    public function __construct(
        private readonly SearchApiManager $searchApiManager,
        private readonly SearchApiComponentRegistry $searchApiComponentRegistry,
        private readonly GoogleCsaRegistry $googleCsaRegistry,
        private readonly DisplaySearchRelatedSettings $displaySearchRelatedSettings,
        private readonly RelatedTermsRenderer $relatedTermsRenderer,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader,
        private readonly MonetizationSettings $monetizationSettings
    )
    {
    }

    public function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        if (!$component->target->forContent()) {
            $request->setRequirements([ViewDataProperty::QUERY], $conditions);
        }

        if ($this->requiresVisymoRelatedTerms($component)) {
            $request->setRequirements([ViewDataProperty::RELATED_TERMS], $conditions);
        }
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        $this->searchApiComponentRegistry->addComponentWithSearchApiDependency($component, $conditions);

        if (!$this->requiresVisymoRelatedTerms($component)) {
            return;
        }

        $fallbackRelatedTermsComponent = $this->getFallbackRelatedTermsComponent($component);

        if ($fallbackRelatedTermsComponent !== null) {
            $this->relatedTermsRenderer->buildRequest($fallbackRelatedTermsComponent, $request, $conditions);

            return;
        }

        $relatedTermsViewDataRequest = $request->relatedTerms()->increaseAmount($component->amount);

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::RELATED_TERMS,
            searchApiViewDataRequest: $relatedTermsViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function handleSearchApiCompleted(
        ComponentInterface $component,
        ViewInterface $view
    ): void
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        $view->getDataRequest()->googleCsa()->addRelatedSearchUnit(
            new GoogleCsaRelatedSearchUnitViewDataRequest(
                amount                  : $component->amount,
                container               : $component->container,
                route                   : $component->route,
                forContent              : $component->target->forContent(),
                addVisymoRelatedTerms   : $this->shouldAddVisymoRelatedTermsToGoogleCsaRequest($component),
                termsUrlParameterEnabled: $component->termsUrlParameterEnabled,
            ),
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        $relatedSearchUnit = $this->googleCsaRegistry->getGoogleCsa()?->relatedSearch()
            ->getUnitByContainer($component->container);

        if ($relatedSearchUnit === null) {
            return '';
        }

        $html = $relatedSearchUnit->getContainerHtml('component component--no-default-space csa csa--loading gr-results');

        $fallbackRelatedTermsComponent = $this->getFallbackRelatedTermsComponent($component);

        if ($fallbackRelatedTermsComponent !== null) {
            $html .= $this->relatedTermsRenderer->render($fallbackRelatedTermsComponent, $view);
        }

        return $html;
    }

    public function renderHeaders(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof GoogleRelatedTermsComponent) {
            throw UnsupportedComponentException::create($component, [GoogleRelatedTermsComponent::class]);
        }

        if ($component->fallbackRelatedTerms === null) {
            return '';
        }

        return $this->relatedTermsRenderer->renderHeaders($component->fallbackRelatedTerms, $view);
    }

    /**
     * {@see GoogleCsaRelatedSearchRequestFactory::getRelatedTerms}
     */
    private function requiresVisymoRelatedTerms(GoogleRelatedTermsComponent $component): bool
    {
        if (!$this->monetizationSettings->relatedTermsEnabled) {
            return false;
        }

        if ($this->getFallbackRelatedTermsComponent($component) !== null) {
            return true;
        }

        if ($component->target->requiresVisymoRelatedTerms()) {
            return true;
        }

        return false;
    }

    private function shouldAddVisymoRelatedTermsToGoogleCsaRequest(GoogleRelatedTermsComponent $component): bool
    {
        $requiresVisymoRelatedTerms = $this->requiresVisymoRelatedTerms($component);

        if (!$requiresVisymoRelatedTerms) {
            return false;
        }

        if ($this->splitTestExtendedReader->isVariantActive('nrt')) {
            return false;
        }

        return true;
    }

    private function getFallbackRelatedTermsComponent(GoogleRelatedTermsComponent $component): ?RelatedTermsComponent
    {
        return $this->displaySearchRelatedSettings->relatedFallbackEnabled
            ? $component->fallbackRelatedTerms
            : null;
    }
}
