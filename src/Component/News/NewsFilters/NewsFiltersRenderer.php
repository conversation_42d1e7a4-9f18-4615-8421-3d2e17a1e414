<?php

declare(strict_types=1);

namespace App\Component\News\NewsFilters;

use App\Component\Generic\SearchFilters\SearchFiltersFactory;
use App\Component\Generic\SearchFilters\SearchFiltersLayout;
use App\Component\Generic\SearchFilters\SearchFiltersRenderer;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\NewsSearch\Filter\NewsCategoryFilter;
use App\NewsSearch\Filter\NewsPeriodFilter;

class NewsFiltersRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly NewsCategoryFilter $newsCategoryFilter,
        private readonly NewsPeriodFilter $newsPeriodFilter,
        private readonly SearchFiltersRenderer $searchFiltersRenderer,
        private readonly SearchFiltersFactory $searchFiltersFactory
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::NEWS_RESULTS,
            ],
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof NewsFiltersComponent) {
            throw UnsupportedComponentException::create($component, [NewsFiltersComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        if (!$viewDataRegistry->getSearchResponses()->hasResults(ViewDataProperty::NEWS_RESULTS)) {
            return '';
        }

        return $this->searchFiltersRenderer->render(
            $this->searchFiltersFactory->createWithLayoutAndFilters(
                layout : SearchFiltersLayout::DEFAULT,
                filters: [
                             $this->newsCategoryFilter->getSearchFilter(),
                             $this->newsPeriodFilter->getSearchFilter(),
                         ],
            ),
            $view,
        );
    }
}
