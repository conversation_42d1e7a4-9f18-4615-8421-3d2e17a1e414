<?php

declare(strict_types=1);

namespace App\Component\Web\WebSearchStatsTitle;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Search\Request\SearchRequestInterface;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Context\Organic\Response\OrganicResponseContext;

final class WebSearchStatsTitleRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly WebSearchStatsTitleFormatter $webSearchStatsTitleFormatter,
        private readonly SearchRequestInterface $searchRequest,
        private readonly Environment $twig
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        if (!$component instanceof WebSearchStatsTitleComponent) {
            throw UnsupportedComponentException::create($component, [WebSearchStatsTitleComponent::class]);
        }

        if ($component->showRandomStats) {
            $request->setRequirements(
                [
                    ViewDataProperty::QUERY,
                ],
            );
        } else {
            $request->setRequirements(
                [
                    ViewDataProperty::QUERY,
                    ViewDataProperty::SEARCH_RESPONSES,
                    ViewDataProperty::ORGANIC_RESULTS,
                ],
            );
        }
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof WebSearchStatsTitleComponent) {
            throw UnsupportedComponentException::create($component, [WebSearchStatsTitleComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        if ($component->showRandomStats) {
            $statistics = $this->webSearchStatsTitleFormatter->formatPageStatistics(
                query              : (string)$viewDataRegistry->getQuery(),
                page               : $this->searchRequest->getPage(),
                totalResults       : random_int(110034445, 9999799800),
                processTime        : random_int(2, 10) / 100,
                highlightProperties: false,
            );
        } else {
            /** @var OrganicResponseContext|null $organicResponse */
            $organicResponse = $view->getDataRegistry()
                ->getSearchResponses()
                ->getSearchResponseContext(ViewDataProperty::ORGANIC_RESULTS);

            $statistics = $this->webSearchStatsTitleFormatter->formatPageStatistics(
                query              : (string)$viewDataRegistry->getQuery(),
                page               : (int)$organicResponse?->getCurrentPage(),
                totalResults       : (int)$organicResponse?->getResultsTotalDisplay(),
                processTime        : $viewDataRegistry->getSearchResponses()->getResponseProcessTime(),
                highlightProperties: false,
            );
        }

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout'     => $component->layout->value,
                'statistics' => $statistics,
            ],
        );
    }
}
