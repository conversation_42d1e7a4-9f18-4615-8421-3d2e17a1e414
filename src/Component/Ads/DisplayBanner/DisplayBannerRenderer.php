<?php

declare(strict_types=1);

namespace App\Component\Ads\DisplayBanner;

use App\GooglePublisherTag\Settings\GooglePublisherTagSettings;
use App\GooglePublisherTag\Tag\GoogleTagRenderHelper;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

final class DisplayBannerRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig,
        private readonly GoogleTagRenderHelper $googleTagRenderHelper,
        private readonly GooglePublisherTagSettings $googlePublisherTagSettings
    )
    {
    }

    public function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof DisplayBannerComponent) {
            throw UnsupportedComponentException::create(
                $component,
                [DisplayBannerComponent::class],
            );
        }

        $adUnitPath = $component->adUnitPath
                      ?? $this->googlePublisherTagSettings->adUnitPath;

        if ($adUnitPath === null) {
            return;
        }

        $sizes = array_map(
            static fn (array $format): array => [
                $format[DisplayBannerResolver::KEY_FORMAT_WIDTH],
                $format[DisplayBannerResolver::KEY_FORMAT_HEIGHT],
            ],
            $component->formats,
        );

        $this->googleTagRenderHelper->registerComponentSlotRequest($component, $adUnitPath, $sizes);
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof DisplayBannerComponent) {
            throw UnsupportedComponentException::create($component, [DisplayBannerComponent::class]);
        }

        $slot = $this->googleTagRenderHelper->getComponentSlot($component);

        if ($slot === null) {
            return '';
        }

        $this->googleTagRenderHelper->registerSlotRendered($slot);

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'layout' => $component->layout->value,
                'slot'   => $slot,
            ],
        );
    }
}
