<?php

declare(strict_types=1);

namespace App\Component\Ads\GoogleAdsTopUnit;

use App\Ads\AdsAmountRegistry;
use App\GoogleCsa\Registry\GoogleCsaRegistry;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;

final class GoogleAdsTopUnitRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly GoogleCsaRegistry $googleCsaRegistry,
        private readonly AdsAmountRegistry $adsAmountRegistry
    )
    {
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof GoogleAdsTopUnitComponent) {
            throw UnsupportedComponentException::create($component, [GoogleAdsTopUnitComponent::class]);
        }

        $this->adsAmountRegistry->increasePreloadedAdsAmount($component->amount);

        $request->googleCsa()
            ->setTopAdAmount($component->amount)
            ->setTopAdContainer($component->container);
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof GoogleAdsTopUnitComponent) {
            throw UnsupportedComponentException::create($component, [GoogleAdsTopUnitComponent::class]);
        }

        $topAdUnit = $this->googleCsaRegistry->getGoogleCsa()?->ads()->getTopUnit();

        return $topAdUnit?->getContainerHtml('component component--no-default-space csa csa--loading ga-results') ?? '';
    }
}
