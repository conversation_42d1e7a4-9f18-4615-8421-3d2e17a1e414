/** @define container */
// stylelint-disable visymo/sort-properties-alphabetically
// stylelint-disable plugin/selector-bem-pattern
.container--dsr {
    --container__column-one_width: 66rem;
    --container__columns_grid-template-areas: ". one .";
    --container__columns_grid-template-columns: auto
        minmax(10rem, var(--container__column-one_width)) auto;

    .section {
        &--footer {
            --container__section_background: #f1f3f4;
        }
    }
}

.container--dsr {
    background: var(--container__background, none);
    border-top: var(--container__border-top, 0.3rem solid var(--brand-primary-color));
    display: flex;
    flex-direction: column;
    min-height: 100vh;
}

/** @define html */
.html--dsr {
    &.html--mode-dark {
        --container__background: #01074b;
        --container__section_color: #ffffff;
        --container__section-highlight_color: var(--brand-primary-color);
        --container__section-secondary_color: #dddddd;
    }
}
