@import "../../../SearchHeader/layout/default/searchHeaderMixins";

/** @define search-header-content */
// stylelint-disable visymo/sort-properties-alphabetically

// Base styles for all content layouts
.search-header-content--default {
    position: relative;
    padding-bottom: 1.5rem;
    padding-top: 1.5rem;

    &.search-header-content--logo-left {
        display: grid;
        column-gap: 1.5rem;
        grid-template-areas: "logo navigation navigation-more search";
        grid-template-columns: auto minmax(0, 1fr) auto 27rem;

        // min-c

        .search-header-content {
            &__categories {
                column-gap: 1.5rem;
                display: flex;
                flex-shrink: 1;
                flex-wrap: wrap;
                justify-content: flex-end;
            }
        }
    }

    @include search-header-search-bar-mobile-full-page;

    .search-header-content {
        // Brand logo
        &__logo {
            align-self: center;
            grid-area: logo;
        }

        &__brand-image {
            height: max-content;
            max-height: 4.4rem;
            max-width: 20rem;
            object-fit: scale-down;
        }

        &__brand-icon {
            display: none;
        }

        &__brand-link {
            display: inline-block;
        }

        // Navigation (for category layouts)
        &__navigation {
            align-self: center;
            grid-area: navigation;
            height: 4rem;
            line-height: 4rem;
            overflow: hidden;
            padding-right: 0.4rem;
        }

        &__categories {
            font-size: 1.6rem;
            font-weight: 700;
        }

        &__category-link {
            color: var(--container__section_color, #000000);
            white-space: nowrap;

            &:hover {
                text-decoration: underline;
            }
        }

        &__category-more {
            color: var(--container__section_color, #000000);
            cursor: pointer;
            display: block;
            font-size: 1.6rem;
            font-weight: 700;
            line-height: 4rem;
            padding-right: 2rem;
            position: relative;
            user-select: none;

            &::before {
                color: var(--container__section_color, #555555);
                content: $vsi-chevron-down;
                position: absolute;
                right: 0;
                transition: 0.2s;
            }
        }

        &__navigation-more {
            /* stylelint-disable-next-line selector-class-pattern */
            &--active .search-header-content__category-more::before {
                transform: rotate(180deg);
            }
        }

        // Auto suggest
        &__auto-suggest {
            --border-color: #dddddd;
            top: 100%;
        }
    }
}
