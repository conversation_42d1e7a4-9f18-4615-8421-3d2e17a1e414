{# @var layout string #}
{# @var logo_position string #}
{# @var logo string|null #}
{# @var logo_style_filter App\Generic\Logo\LogoStyleFilter|null #}
{# @var query string #}
{# @var show_categories bool #}
{{ component_javascript(['searchHeaderContent', 'searchHeaderContentCategory']) }}
{{ component_style('searchHeaderContentDefault') }}

{% set component_class = 'search-header-content' %}
{% set additional_classes = [component_class ~ '--logo-' ~ logo_position] %}
{% set show_categories = show_categories|default(false) %}

<div class="{{ component_class(component_class, [layout], [], additional_classes) }}"{{ delayed_container_attributes() }}>
    {% if logo_position != 'none' %}
        <div class="{{ component_class }}__logo">
            <a href="{{ persistent_new_search_path('route_home') }}" class="{{ component_class }}__brand-link">
                <img src="{{ brand_image_base64(logo) }}" alt="{{ brand_name }}" class="{{ component_class }}__brand-image {{ render_logo_class(logo_style_filter) }}"/>
                <img src="{{ brand_image_base64('favicon.png') }}" alt="{{ brand_name }}" class="{{ component_class }}__brand-icon {{ render_logo_class(logo_style_filter) }}"/>
            </a>
        </div>
    {% endif %}

    {% if show_categories %}
        <div class="{{ component_class }}__navigation">
            <ul class="{{ component_class }}__categories">
                {% for category in content_page_categories %}
                    <li class="{{ component_class }}__category">
                        <a href="{{ generate_category_url(category) }}" class="{{ component_class }}__category-link">{{ category.title }}</a>
                    </li>
                {% endfor %}
            </ul>
        </div>
        <div class="{{ component_class }}__navigation-more">
            <span class="{{ component_class }}__category-more vsi">{{ 'search_header.more_categories'|trans }}</span>
        </div>
    {% endif %}

    {{ search_bar|raw }}
</div>
