<?php

declare(strict_types=1);

namespace App\Component\Generic\SearchHeader;

use App\Brand\Settings\BrandSettingsHelper;
use App\Component\Generic\OverlayMenu\OverlayMenuManager;
use App\ContentPage\Helper\ContentPageCategoryHelper;
use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use App\Menu\Helper\MenuHelper;
use App\Search\Registry\RouteRegistry;
use App\SearchApi\SearchApiManager;
use App\SplitTest\SplitTestExtendedReaderInterface;
use Twig\Environment;
use Visymo\CompositeSearchApiClient\Domain\SearchEngine\Response\ContentPage\ContentPageNestedCategory;

final class SearchHeaderRenderer extends AbstractComponentRenderer
{
    private const int MAX_CONTENT_PAGE_CATEGORIES = 5;

    public function __construct(
        private readonly Environment $twig,
        private readonly OverlayMenuManager $overlayMenuManager,
        private readonly ContentPageCategoryHelper $contentPageCategoryHelper,
        private readonly SearchApiManager $searchApiManager,
        private readonly SearchHeaderSearchBarRenderer $searchHeaderSearchBarRenderer,
        private readonly BrandSettingsHelper $brandSettingsHelper,
        private readonly RouteRegistry $routeRegistry,
        private readonly MenuHelper $menuHelper,
        private readonly SplitTestExtendedReaderInterface $splitTestExtendedReader
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        if (!$component instanceof SearchHeaderComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderComponent::class]);
        }

        if ($component->layout->requiresContentPageCategories()) {
            $request
                ->setRequirements(
                    [
                        ViewDataProperty::CONTENT_PAGE_CATEGORIES,
                        ViewDataProperty::QUERY,
                    ],
                );
        } else {
            $request->setRequirements(
                [
                    ViewDataProperty::QUERY,
                ],
            );
        }
    }

    protected function buildRequest(
        ComponentInterface $component,
        ViewDataRequest $request,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof SearchHeaderComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderComponent::class]);
        }

        if (!$component->layout->requiresContentPageCategories()) {
            return;
        }

        $contentPageCategoriesViewDataRequest = $request
            ->contentPageCategories()
            ->enable()
            ->setMaxLevel(0)
            ->setHasImage(true)
            ->setIsAdult(false);

        $this->searchApiManager->registerComponentSearchRequest(
            component               : $component,
            viewDataProperty        : ViewDataProperty::CONTENT_PAGE_CATEGORIES,
            searchApiViewDataRequest: $contentPageCategoriesViewDataRequest,
            conditions              : $conditions,
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof SearchHeaderComponent) {
            throw UnsupportedComponentException::create($component, [SearchHeaderComponent::class]);
        }

        $darkModeLogo = $this->splitTestExtendedReader->isVariantActive('wsdm') || $component->logoDarkMode;

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'view'                           => $view,
                'query'                          => $component->showSearchQuery
                    ? $view->getDataRegistry()->getQuery()
                    : null,
                'layout'                         => $component->layout->value,
                'autofocus'                      => $component->autofocus,
                'content_page_categories'        => $this->getContentPageCategories($component, $view),
                'show_background_on_desktop'     => $component->showBackgroundOnDesktop,
                'show_background_on_mobile'      => $component->showBackgroundOnMobile,
                'show_background_on_tablet'      => $component->showBackgroundOnTablet,
                'show_hamburger_menu_on_desktop' => $component->showHamburgerMenuOnDesktop,
                'show_hamburger_menu_on_mobile'  => $component->showHamburgerMenuOnMobile,
                'show_hamburger_menu_on_tablet'  => $component->showHamburgerMenuOnTablet,
                'show_main_menu_more_on_desktop' => $component->showMainMenuMoreOnDesktop,
                'show_main_menu_more_on_mobile'  => $component->showMainMenuMoreOnMobile,
                'show_main_menu_more_on_tablet'  => $component->showMainMenuMoreOnTablet,
                'show_main_menu_on_desktop'      => $component->showMainMenuOnDesktop,
                'show_main_menu_on_mobile'       => $component->showMainMenuOnMobile,
                'show_main_menu_on_tablet'       => $component->showMainMenuOnTablet,
                'show_sub_menu_on_desktop'       => $component->showSubMenuOnDesktop,
                'show_sub_menu_on_mobile'        => $component->showSubMenuOnMobile,
                'show_sub_menu_on_tablet'        => $component->showSubMenuOnTablet,
                'show_favicon'                   => $component->layout->getShowFavicon(),
                'logo'                           => $component->layout->getLogo($darkModeLogo)?->value,
                'logo_style_filter'              => $component->logoStyleFilter,
                'component_space_modifiers'      => $component->componentSpaceModifiers,
                'overlay_menu'                   => $this->overlayMenuManager->renderOverlayMenu($view),
                'search_bar'                     => $this->searchHeaderSearchBarRenderer->renderSearchBar(
                    $component,
                    $view,
                ),
                'search_route'                   => $this->routeRegistry->getSearchRoute(),
                'brand_name'                     => $this->brandSettingsHelper->getSettings()->getName(),
                'menu_helper'                    => $this->menuHelper,
            ],
        );
    }

    /**
     * @return ContentPageNestedCategory[]
     */
    private function getContentPageCategories(SearchHeaderComponent $component, ViewInterface $view): array
    {
        if (!$component->layout->requiresContentPageCategories()) {
            return [];
        }

        return $this->contentPageCategoryHelper->getPreferenceContentPageCategories(
            $view->getDataRegistry()->getContentPageCategories($component)->results,
            self::MAX_CONTENT_PAGE_CATEGORIES,
        );
    }
}
