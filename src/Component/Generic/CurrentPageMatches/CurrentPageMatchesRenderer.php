<?php

declare(strict_types=1);

namespace App\Component\Generic\CurrentPageMatches;

use App\Component\Generic\AbstractCondition\AbstractConditionRenderer;
use App\ContentPage\Paragraph\ParagraphsAmountRegistry;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ScalarValueCondition;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\SegmentInterface;
use App\JsonTemplate\View\ViewInterface;
use App\Search\Request\SearchRequestInterface;

final class CurrentPageMatchesRenderer extends AbstractConditionRenderer
{
    public function __construct(
        private readonly SearchRequestInterface $searchRequest,
        private readonly ParagraphsAmountRegistry $paragraphsAmountRegistry
    )
    {
    }

    public function build(
        ComponentInterface $component,
        ViewInterface $view,
        ViewDataConditionCollection $conditions
    ): void
    {
        if (!$component instanceof CurrentPageMatchesComponent) {
            throw UnsupportedComponentException::create($component, [CurrentPageMatchesComponent::class]);
        }

        $this->paragraphsAmountRegistry->registerParagraphsPerPage(
            page              : $component->getPage(),
            matchingSegment   : $component->getMatchingSegment(),
            nonMatchingSegment: $component->getNonMatchingSegment(),
        );

        parent::build($component, $view, $conditions);
    }

    protected function getActiveSegment(ComponentInterface $component): SegmentInterface
    {
        if (!$component instanceof CurrentPageMatchesComponent) {
            throw UnsupportedComponentException::create($component, [CurrentPageMatchesComponent::class]);
        }

        if ($this->searchRequest->getPage() === $component->getPage()) {
            return $component->getMatchingSegment();
        }

        return $component->getNonMatchingSegment();
    }

    protected function getConditions(ComponentInterface $component, bool $expectedResult): ViewDataConditionCollection
    {
        if (!$component instanceof CurrentPageMatchesComponent) {
            throw UnsupportedComponentException::create($component, [CurrentPageMatchesComponent::class]);
        }

        return new ViewDataConditionCollection(
            [
                new ScalarValueCondition(
                    value         : $this->searchRequest->getPage(),
                    expectedValue : $component->getPage(),
                    expectedResult: $expectedResult,
                ),
            ],
        );
    }
}
