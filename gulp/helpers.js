import filesystem from 'fs';
import gulp from 'gulp';
import * as fs from 'node:fs';
import order from 'ordered-read-streams';
import {Readable} from 'streamx';

export function createBaseConfig() {
    return {
        js: {
            sourceFiles: [
                './src/Component/**/*.js',
                './resources/shared/assets/js/SplitTests/*.js',
            ]
        },
        scss: {
            sourceFiles: [
                './src/Component/**/*.scss',
                './resources/shared/assets/scss/SplitTests/*.scss',
            ],
            prependToAllImports: [
                './resources/shared/assets/scss/_defaultVariables.scss',
                './resources/shared/assets/scss/_defaultFontVariables.scss',
            ],
        },
    };
}

export function getConfig() {
    if (!filesystem.existsSync(`./config`)) {
        console.error(`Folder ./config was not found`);

        return null;
    }

    const requireFilePath = `./resources/shared/assets/js/require.json`;

    return {
        projectFolder: `./`,
        tempFolder: `./tmp`,
        buildFolder: `./public/build`,
        sharedFolder: `./public/build/shared`,
        fonts: {
            sourceFiles: `./resources/shared/assets/fonts/**/*`,
            targetFolder: `/fonts`
        },
        scss: {
            brandEntryFiles: `./resources/shared/assets/scss/BrandEntries/*.scss`,
            sharedEntryFiles: `./resources/shared/assets/scss/Entries/*.scss`,
            watchFiles: [
                `./brands/**/assets/*.scss`,
                './src/Component/**/*.scss',
                `./resources/prototype/assets/scss/**/*.scss`,
                `./resources/shared/assets/scss/**/*.scss`,
                './resources/shared/assets/css/split_tests/*.css',
            ]
        },
        js: {
            require: requireFilePath,
            sharedEntriesFolder: `./resources/shared/assets/js/Entries`,
            prototypeEntriesFolder: `./resources/prototype/assets/js/Entries`,
            watchFiles: [
                requireFilePath,
                `./resources/shared/assets/js/**/*.js`,
                `./resources/prototype/assets/js/**/*.js`,
                './src/Component/**/*.js',
            ],
        },
        images: {
            sharedStyleImageFiles: `./src/Resources/shared_style/images/**/*`,
        }
    };
}

export function copyAssetsToBrands(filepathArray, tempFolder) {
    function copyAssets(callback) {
        const tasksCompleted = [];

        filepathArray.forEach((directory) => {
            gulp.src([`${tempFolder}/**/*`])
                .pipe(gulp.dest(directory))
                .on('end', () => {
                    tasksCompleted.push(directory);

                    if (tasksCompleted.length === filepathArray.length) {
                        callback();
                    }
                });
        });
    }

    return [
        new Readable({
            read: function (callback) {
                copyAssets(() => {
                    callback();

                    // Signal completion of this task
                    this.push(null);
                });
            },
        }),
    ];
}

export function mkdirSafe(path) {
    const exists = fs.existsSync(path);

    if (!exists) {
        fs.mkdirSync(path, {recursive: true});
    }
}

export function mergedStreams(streams, cb) {
    return order(streams).on('end', cb);
}
