<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Generator\FileReplacer;

use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentLayout;
use Visymo\DevelopBundle\GenerateComponent\Generator\Model\ComponentName;

final class ComponentScssFileReplacer implements ComponentFileReplacerInterface
{
    public function supportsFileExtension(): string
    {
        return 'scss';
    }

    public function replaceFileName(
        string $fileName,
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): string
    {
        return str_replace(
            sprintf('%s%s', $sourceComponentName->titleCase, $sourceLayout->pascalCase),
            sprintf('%s%s', $destinationComponentName->titleCase, $destinationLayout->pascalCase),
            $fileName,
        );
    }

    public function replaceContent(
        string $content,
        ComponentName $sourceComponentName,
        ComponentLayout $sourceLayout,
        ComponentName $destinationComponentName,
        ComponentLayout $destinationLayout
    ): string
    {
        return str_replace(
            [
                $sourceComponentName->kebabCase,
                sprintf('--%s', $sourceLayout->kebabCase),
            ],
            [
                $destinationComponentName->kebabCase,
                sprintf('--%s', $destinationLayout->kebabCase),
            ],
            $content,
        );
    }
}
