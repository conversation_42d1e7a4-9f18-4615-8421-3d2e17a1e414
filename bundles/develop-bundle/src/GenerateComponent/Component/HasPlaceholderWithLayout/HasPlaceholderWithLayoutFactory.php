<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\HasPlaceholderWithLayout;

use Visymo\BrandWebsiteBundle\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionResolver;
use Visymo\BrandWebsiteBundle\JsonTemplate\Component\ComponentFactory;
use Visymo\BrandWebsiteBundle\JsonTemplate\Component\ComponentFactoryInterface;
use Visymo\BrandWebsiteBundle\JsonTemplate\Component\ComponentInterface;
use Visymo\BrandWebsiteBundle\JsonTemplate\Component\Layout\LayoutInterface;

final class HasPlaceholderWithLayoutFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return HasPlaceholderWithLayoutComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $matchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_YES]
        );
        $nonMatchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_NO]
        );

        return new HasPlaceholderWithLayoutComponent(
            layout             : HasPlaceholderWithLayoutLayout::from($options[LayoutInterface::KEY]),
            matchingChildren   : $matchingChildren,
            nonMatchingChildren: $nonMatchingChildren
        );
    }
}
