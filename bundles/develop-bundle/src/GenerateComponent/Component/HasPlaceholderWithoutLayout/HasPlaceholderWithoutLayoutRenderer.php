<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\HasPlaceholderWithoutLayout;

use App\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\Condition\ViewDataConditionCollection;
use App\JsonTemplate\View\Data\Condition\ViewDataPropertySetCondition;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\SegmentInterface;
use App\JsonTemplate\View\ViewInterface;

final class HasPlaceholderWithoutLayoutRenderer extends AbstractSearchApiConditionRenderer
{
    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
            ],
        );
    }

    public function getActiveSegment(ComponentInterface $component, ViewInterface $view): SegmentInterface
    {
        if (!$component instanceof HasPlaceholderWithoutLayoutComponent) {
            throw UnsupportedComponentException::create($component, [HasPlaceholderWithoutLayoutComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        return $viewDataRegistry->getQuery() !== null
            ? $component->getMatchingSegment()
            : $component->getNonMatchingSegment();
    }

    public function getConditions(ComponentInterface $component, bool $expectedResult): ViewDataConditionCollection
    {
        return new ViewDataConditionCollection(
            [
                new ViewDataPropertySetCondition(ViewDataProperty::QUERY, $expectedResult, false),
            ],
        );
    }
}
