<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\HasPlaceholderWithoutLayout;

use Visymo\BrandWebsiteBundle\Component\Generic\AbstractSearchApiCondition\AbstractSearchApiConditionResolver;
use Visymo\BrandWebsiteBundle\JsonTemplate\Component\ComponentFactory;
use Visymo\BrandWebsiteBundle\JsonTemplate\Component\ComponentFactoryInterface;
use Visymo\BrandWebsiteBundle\JsonTemplate\Component\ComponentInterface;

final class HasPlaceholderWithoutLayoutFactory implements ComponentFactoryInterface
{
    public static function getSupportedComponent(): string
    {
        return HasPlaceholderWithoutLayoutComponent::class;
    }

    /**
     * @inheritDoc
     */
    public function create(array $options, ComponentFactory $componentFactory): ComponentInterface
    {
        $matchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_YES]
        );
        $nonMatchingChildren = $componentFactory->createMultipleFromOptions(
            $options[AbstractSearchApiConditionResolver::KEY_NO]
        );

        return new HasPlaceholderWithoutLayoutComponent(
            matchingChildren   : $matchingChildren,
            nonMatchingChildren: $nonMatchingChildren
        );
    }
}
