<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\Placeholder;

use App\JsonTemplate\Component\Layout\LayoutInterface;

enum PlaceholderLayout: string implements LayoutInterface
{
    case DEFAULT = 'default';

    public static function getDefault(): self
    {
        return self::DEFAULT;
    }

    public function getTwigTemplate(): string
    {
        return match ($this) {
            self::DEFAULT => '@component/Section/Placeholder/layout/default/placeholder-default.html.twig',
        };
    }
}
