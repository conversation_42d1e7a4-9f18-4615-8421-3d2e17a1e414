<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\GenerateComponent\Component\PlaceholderWithSpaceModifier;

use App\JsonTemplate\Component\AbstractComponentRenderer;
use App\JsonTemplate\Component\ComponentInterface;
use App\JsonTemplate\Component\Exception\UnsupportedComponentException;
use App\JsonTemplate\View\Data\ViewDataProperty;
use App\JsonTemplate\View\DataRequest\ViewDataRequest;
use App\JsonTemplate\View\ViewInterface;
use Twig\Environment;

final class PlaceholderWithSpaceModifierRenderer extends AbstractComponentRenderer
{
    public function __construct(
        private readonly Environment $twig
    )
    {
    }

    protected function registerDataRequirements(
        ComponentInterface $component,
        ViewDataRequest $request
    ): void
    {
        $request->setRequirements(
            [
                ViewDataProperty::QUERY,
            ],
        );
    }

    public function render(ComponentInterface $component, ViewInterface $view): string
    {
        if (!$component instanceof PlaceholderWithSpaceModifierComponent) {
            throw UnsupportedComponentException::create($component, [PlaceholderWithSpaceModifierComponent::class]);
        }

        $viewDataRegistry = $view->getDataRegistry();

        return $this->twig->render(
            $component->layout->getTwigTemplate(),
            [
                'query'                     => $viewDataRegistry->getQuery(),
                'layout'                    => $component->layout->value,
                'component_space_modifiers' => $component->componentSpaceModifiers,
            ],
        );
    }
}
