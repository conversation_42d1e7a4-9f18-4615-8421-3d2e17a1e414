<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonSchema\OptionsResolver;

use Visymo\BrandWebsiteBundle\Generic\Validator\AllowedEnumCasesValidator;
use Visymo\BrandWebsiteBundle\Generic\Validator\AllowedEnumCaseValidator;
use Visymo\Shared\Domain\OptionsResolver\OptionDefinitionInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionsResolverInterface;
use Visymo\Shared\Domain\OptionsResolver\OptionType;
use Visymo\Shared\Domain\Validator\AllowedArrayValueTypeValidator;
use Visymo\Shared\Domain\Validator\AllowedPatternValidator;
use Visymo\Shared\Domain\Validator\AllowedValuesValidator;
use Visymo\Shared\Domain\Validator\AllowedValueValidator;
use Visymo\Shared\Domain\Validator\GreaterThanOrEqualValidator;
use Visymo\Shared\Domain\Validator\GreaterThanValidator;
use Visymo\Shared\Domain\Validator\LesserThanOrEqualValidator;
use Visymo\Shared\Domain\Validator\LesserThanValidator;
use Visymo\Shared\Domain\Validator\NotEmptyValidator;
use Visymo\Shared\Domain\Validator\ValidatorInterface;

class DebugOptionsResolver implements OptionsResolverInterface
{
    /** @var DebugOptionDefinition[] */
    private array $options = [];

    /**
     * @return string[]
     */
    public function getRequiredProperties(): array
    {
        $requiredProperties = [];

        foreach ($this->getOptions() as $option) {
            if ($option->isRequired()) {
                $requiredProperties[] = $option->field;
            }
        }

        sort($requiredProperties);

        return $requiredProperties;
    }

    /**
     * @return array<string, mixed>
     */
    public function getProperties(): array
    {
        $properties = [];

        foreach ($this->getOptions() as $option) {
            if ($option->isNullable()) {
                $property = [
                    'type' => [
                        $option->getType(),
                        'null',
                    ],
                ];
            } else {
                $property = [
                    'type' => $option->getType(),
                ];
            }

            if (!$option->isRequired()) {
                $property['default'] = $option->getDefaultValue();
            }

            foreach ($option->getValidators() as $validator) {
                $property += $this->getValidatorSettings($validator, $option->getType());
            }

            if ($option->getNestedResolver() !== null) {
                $property['properties'] = $option->getNestedResolver()->getProperties();
            }

            $properties[$option->field] = $property;
        }

        ksort($properties);

        return $properties;
    }

    public function ignoreUndefinedOptions(bool $ignoreUndefinedOptions): void
    {
        // Only defined options are allowed
    }

    public function define(string $field): OptionDefinitionInterface
    {
        $this->options[$field] = new DebugOptionDefinition(
            field: $field
        );

        return $this->options[$field];
    }

    /**
     * @inheritDoc
     */
    public function resolve(array $options): array
    {
        return [];
    }

    /**
     * @return array<string, DebugOptionDefinition>
     */
    public function getOptions(): array
    {
        return $this->options;
    }

    /**
     * @return array<string, mixed>
     */
    protected function getValidatorSettings(ValidatorInterface $validator, string $optionType): array
    {
        $validatorProperties = $this->getValidatorClassProperties($validator);

        $settings = [];
        $validatorClassName = key($validatorProperties);
        $validatorValue = current($validatorProperties);

        switch ($validatorClassName) {
            case AllowedEnumCaseValidator::class:
            case AllowedValueValidator::class:
                $settings['enum'] = $validatorValue['allowedValues'];
                sort($settings['enum']);

                break;
            case AllowedArrayValueTypeValidator::class:
                $settings['items'] = [
                    'type' => 'string',
                ];

                break;
            case AllowedEnumCasesValidator::class:
            case AllowedValuesValidator::class:
                $settings['items'] = [
                    'type' => 'string',
                    'enum' => $validatorValue['allowedValues'],
                ];
                sort($settings['items']['enum']);

                break;
            case AllowedPatternValidator::class:
                $settings['pattern'] = $validatorValue['allowedPattern'];
                $settings['pattern'] = substr((string)$settings['pattern'], 1, -1);

                break;
            case GreaterThanOrEqualValidator::class:
                $settings['minimum'] = $validatorValue['min'];

                break;
            case GreaterThanValidator::class:
                $settings['exclusiveMinimum'] = $validatorValue['min'];

                break;
            case LesserThanOrEqualValidator::class:
                $settings['maximum'] = $validatorValue['max'];

                break;
            case LesserThanValidator::class:
                $settings['exclusiveMaximum'] = $validatorValue['max'];

                break;
            case NotEmptyValidator::class:
                if ($optionType === OptionType::TYPE_ARRAY) {
                    $settings['minItems'] = 1;
                }

                break;
            default:
                throw new \RuntimeException(sprintf('Unsupported validator "%s"', $validatorClassName));
        }

        return $settings;
    }

    /**
     * @return mixed[]
     */
    private function getValidatorClassProperties(ValidatorInterface $object): array
    {
        $properties = [];
        $validatorClass = $object::class;
        $reflectionObject = new \ReflectionObject($object);

        $isEnumValidator = in_array(
            $validatorClass,
            [
                AllowedEnumCaseValidator::class,
                AllowedEnumCasesValidator::class,
            ],
            true
        );

        if ($isEnumValidator) {
            /** @var \ReflectionObject $reflectionObject */
            $reflectionObject = $reflectionObject->getParentClass();
        }

        foreach ($reflectionObject->getProperties() as $reflectionProperty) {
            $name = $reflectionProperty->getName();
            $reflectionProperty->setAccessible(true);
            $properties[$name] = $reflectionProperty->getValue($object);
        }

        return [
            $validatorClass => $properties,
        ];
    }
}
