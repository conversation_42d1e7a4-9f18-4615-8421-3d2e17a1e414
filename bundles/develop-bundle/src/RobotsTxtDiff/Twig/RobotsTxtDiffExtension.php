<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\RobotsTxtDiff\Twig;

use Twig\Extension\AbstractExtension;
use Twig\TwigFunction;
use Visymo\DevelopBundle\RobotsTxtDiff\Result\RobotsTxtResultRegistry;

final class RobotsTxtDiffExtension extends AbstractExtension
{
    public function __construct(private readonly RobotsTxtResultRegistry $robotsTxtResultRegistry)
    {
    }

    /**
     * @return TwigFunction[]
     */
    public function getFunctions(): array
    {
        return [
            new TwigFunction(
                'robots_txt_versions_by_type',
                $this->versionsByType(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'robots_txt_versions_projects_by_type_and_version',
                $this->getProjectsByTypeAndVersion(...),
                ['is_safe' => ['html']],
            ),
            new TwigFunction(
                'robots_txt_versions_has_remove_suggestions',
                $this->hasRemoveResultSuggestions(...),
                ['is_safe' => ['html']],
            ),
        ];
    }

    /**
     * @return int[]
     */
    private function versionsByType(string $type): array
    {
        return $this->robotsTxtResultRegistry->getVersionsByType($type);
    }

    /**
     * @return string[]
     */
    private function getProjectsByTypeAndVersion(string $type, int $version): array
    {
        return $this->robotsTxtResultRegistry->getProjectsByTypeAndVersion($type, $version);
    }

    private function hasRemoveResultSuggestions(string $project, string $type, int $version): bool
    {
        return $this->robotsTxtResultRegistry->hasRemoveResultSuggestions($project, $type, $version);
    }
}
