<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\JsonTemplateDiff\Controller;

use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Attribute\Route;
use Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilter;
use Visymo\DevelopBundle\JsonTemplateDiff\Filter\JsonTemplateDiffFilterOptions;
use Visymo\DevelopBundle\JsonTemplateDiff\Request\JsonTemplateDiffRequestInterface;
use Visymo\DevelopBundle\JsonTemplateDiff\Result\JsonTemplateResultRegistry;

final class JsonTemplateDiffController extends AbstractController
{
    public function __construct(
        private readonly JsonTemplateResultRegistry $jsonTemplateResultRegistry,
        private readonly JsonTemplateDiffRequestInterface $jsonTemplateDiffRequest,
        private readonly JsonTemplateDiffFilter $jsonTemplateDiffFilter,
        private readonly JsonTemplateDiffFilterOptions $jsonTemplateDiffFilterOptions
    )
    {
    }

    #[Route(path: '/dev/json-template-diff', name: 'route_develop_json_template_diff', methods: ['GET'])]
    public function index(): Response
    {
        $activeResult = null;
        $activeFilter = $this->jsonTemplateDiffFilter;

        if ($this->jsonTemplateDiffRequest->getModuleContentVersionId() !== null) {
            $activeResult = $this->jsonTemplateResultRegistry->getFirstResultByModuleContentVersionId(
                $this->jsonTemplateDiffRequest->getModuleContentVersionId(),
            );

            if ($activeFilter->module !== null && $activeFilter->module !== $activeResult?->module) {
                $activeResult = null;
            }
        }

        if ($activeResult?->isJsonTemplateOverride() === true) {
            $sharedResult = $this->jsonTemplateResultRegistry->getModuleResult(
                module                : $activeResult->module,
                includeFilteredResults: true,
            )->getSharedResult($activeResult);
        } else {
            $sharedResult = null;
        }

        return $this->render(
            '@develop/json_template_diff/json_template_diff.html.twig',
            [
                'json_template_result_registry' => $this->jsonTemplateResultRegistry,
                'filter_options'                => $this->jsonTemplateDiffFilterOptions,
                'active_filter'                 => $activeFilter,
                'active_result'                 => $activeResult,
                'shared_result'                 => $sharedResult,
            ],
        );
    }
}
