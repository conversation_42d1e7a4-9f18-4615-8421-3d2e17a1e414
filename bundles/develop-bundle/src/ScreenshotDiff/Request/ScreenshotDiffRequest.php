<?php

declare(strict_types=1);

namespace Visymo\DevelopBundle\ScreenshotDiff\Request;

use App\Http\Request\Manager\RequestManagerInterface;
use App\Http\Request\Normalizer\RequestPropertyNormalizerInterface;
use Visymo\DevelopBundle\ScreenshotDiff\Image\ImageType;

final class ScreenshotDiffRequest implements ScreenshotDiffRequestInterface
{
    private string $frontendTest;

    private string $imageType;

    public function __construct(
        private readonly RequestManagerInterface $requestManager,
        private readonly RequestPropertyNormalizerInterface $requestPropertyNormalizer
    )
    {
    }

    public function getFrontendTest(): ?string
    {
        if (!isset($this->frontendTest)) {
            $this->frontendTest = $this->requestManager->queryBag()->getString(self::PARAMETER_FRONTEND_TEST);
        }

        return $this->requestPropertyNormalizer->getString($this->frontendTest);
    }

    public function getImageType(): ?ImageType
    {
        if (!isset($this->imageType)) {
            $this->imageType = $this->requestManager->queryBag()->getString(self::PARAMETER_IMAGE_TYPE);
        }

        return ImageType::tryFrom($this->imageType);
    }

    /**
     * @inheritDoc
     */
    public function toArray(): array
    {
        return [
            self::PARAMETER_FRONTEND_TEST => $this->getFrontendTest(),
            self::PARAMETER_IMAGE_TYPE    => $this->getImageType(),
        ];
    }
}
