<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\ConversionTracking\Endpoint\GoogleRelatedTerms;

use App\ConversionTracking\Conversion\Conversion;
use App\ConversionTracking\Conversion\ConversionEventType;
use App\ConversionTracking\Conversion\ConversionRelatedType;
use App\ConversionTracking\Logging\ConversionLogExtra;
use App\ConversionTracking\TrackingOrder\Type\RandomTrackingOrderFactory;

readonly class PartnerGoogleRelatedTermsConversionFactory
{
    public function __construct(
        private RandomTrackingOrderFactory $randomTrackingOrderFactory
    )
    {
    }

    public function create(): Conversion
    {
        $trackingOrder = $this->randomTrackingOrderFactory->create();

        return new Conversion(
            trackingOrder: $trackingOrder,
            eventType    : ConversionEventType::CLICK_RELATED,
            extraLogData : [
                               ConversionLogExtra::CONVERSION_RELATED_TYPE => ConversionRelatedType::GOOGLE_ADSENSE,
                           ],
        );
    }
}
