<?php

declare(strict_types=1);

namespace Visymo\VisymoLabelBundle\PageviewConversion\Tracking;

use Visymo\BrandWebsiteBundle\Brand\Settings\BrandSettingsHelper;
use Visymo\BrandWebsiteBundle\ConversionTracking\Endpoint\EndpointResponse;
use Visymo\BrandWebsiteBundle\ConversionTracking\Endpoint\EndpointResponseInterface;
use Visymo\BrandWebsiteBundle\ConversionTracking\Logging\ConversionLogger;
use Visymo\BrandWebsiteBundle\PageviewConversion\Tracking\PageviewConversionLandingRelatedHandlerInterface;
use Visymo\VisymoLabelBundle\ConversionTracking\Tracking\VisymoConversionTracker;

readonly class PageviewConversionLandingRelatedHandler implements PageviewConversionLandingRelatedHandlerInterface
{
    public function __construct(
        private PageviewConversionLandingRelatedFactory $pageviewConversionLandingRelatedFactory,
        private VisymoConversionTracker $visymoConversionTracker,
        private ConversionLogger $conversionLogger,
        private BrandSettingsHelper $brandSettingsHelper
    )
    {
    }

    public function handle(): EndpointResponseInterface
    {
        $brandSettings = $this->brandSettingsHelper->getSettings();
        $conversion = $this->pageviewConversionLandingRelatedFactory->create();

        if ($brandSettings->getPartnerSlug() !== null) {
            $this->conversionLogger->logConversion($conversion);

            return new EndpointResponse();
        }

        $conversionTrackingResult = $this->visymoConversionTracker->track($conversion);

        $this->conversionLogger->logConversion($conversion, $conversionTrackingResult);

        return new EndpointResponse(
            $conversionTrackingResult->getClientSideTrackingUrl(),
        );
    }
}
